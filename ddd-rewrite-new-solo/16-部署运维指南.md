# 超级个人全栈开发 - 部署运维指南

## 1. 部署运维总览

### 1.1 超级个人运维理念
- **简化优先**：选择最简单可靠的部署和运维方案
- **自动化驱动**：最大化自动化部署和监控，减少手工操作
- **成本控制**：在性能和成本之间找到最佳平衡点
- **渐进式扩展**：支持从单机部署到集群部署的平滑升级
- **可观测性**：完整的监控、日志、追踪体系

### 1.2 部署架构演进路径
```mermaid
graph TB
    subgraph "阶段1: 单机部署 (MVP)"
        SINGLE[单机Docker Compose<br/>开发验证阶段]
        SINGLE_COMPONENTS[应用+数据库+Redis<br/>同一台服务器]
    end
    
    subgraph "阶段2: 基础集群 (生产就绪)"
        LB[负载均衡器<br/>Nginx/Traefik]
        APP_CLUSTER[应用集群<br/>多个应用实例]
        DB_CLUSTER[数据库集群<br/>PostgreSQL主从]
        CACHE_CLUSTER[缓存集群<br/>Redis Cluster]
    end
    
    subgraph "阶段3: 云原生 (规模化)"
        K8S[Kubernetes集群<br/>容器编排]
        MICROSERVICES[微服务架构<br/>服务拆分]
        CLOUD_DB[云数据库<br/>RDS/云Redis]
        CDN[CDN加速<br/>静态资源分发]
    end
    
    subgraph "监控体系 (全阶段)"
        PROMETHEUS[Prometheus<br/>指标监控]
        GRAFANA[Grafana<br/>可视化面板]
        LOKI[Loki<br/>日志聚合]
        JAEGER[Jaeger<br/>链路追踪]
    end
    
    SINGLE --> LB
    SINGLE_COMPONENTS --> APP_CLUSTER
    SINGLE_COMPONENTS --> DB_CLUSTER
    SINGLE_COMPONENTS --> CACHE_CLUSTER
    
    LB --> K8S
    APP_CLUSTER --> MICROSERVICES
    DB_CLUSTER --> CLOUD_DB
    CACHE_CLUSTER --> CDN
    
    PROMETHEUS --> GRAFANA
    LOKI --> GRAFANA
    JAEGER --> GRAFANA
```

## 2. Docker容器化部署

### 2.1 应用容器化配置
```dockerfile
# 后端应用Dockerfile
FROM openjdk:17-jdk-slim as builder

WORKDIR /app
COPY gradlew .
COPY gradle gradle
COPY build.gradle.kts .
COPY settings.gradle.kts .
COPY src src

RUN chmod +x gradlew
RUN ./gradlew build -x test

FROM openjdk:17-jre-slim

WORKDIR /app

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制应用文件
COPY --from=builder /app/build/libs/*.jar app.jar

# 设置文件权限
RUN chown -R appuser:appuser /app
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# JVM优化参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseContainerSupport"

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]

# 前端应用Dockerfile (Vue 3)
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### 2.2 Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  # 后端应用
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: procurement-backend
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=**********************************************
      - SPRING_DATASOURCE_USERNAME=procurement_user
      - SPRING_DATASOURCE_PASSWORD=${DB_PASSWORD}
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - procurement-network
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: procurement-frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    networks:
      - procurement-network
    volumes:
      - ./nginx/ssl:/etc/nginx/ssl:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL数据库
  postgres:
    image: postgres:16
    container_name: procurement-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=procurement_db
      - POSTGRES_USER=procurement_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - procurement-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U procurement_user -d procurement_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: procurement-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - procurement-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: procurement-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - procurement-network

  # 监控 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: procurement-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - procurement-network

  # 监控 - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: procurement-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - procurement-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  procurement-network:
    driver: bridge
```

### 2.3 环境配置管理
```bash
# .env.example
# 数据库配置
DB_PASSWORD=your_secure_db_password
POSTGRES_DB=procurement_db
POSTGRES_USER=procurement_user

# Redis配置
REDIS_PASSWORD=your_secure_redis_password

# 应用配置
JWT_SECRET=your_jwt_secret_key_at_least_32_characters
ENCRYPTION_KEY=your_encryption_key_32_characters

# 监控配置
GRAFANA_PASSWORD=your_grafana_admin_password

# 外部服务配置
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=your_smtp_username
SMTP_PASSWORD=your_smtp_password

# 对象存储配置
S3_ACCESS_KEY=your_s3_access_key
S3_SECRET_KEY=your_s3_secret_key
S3_BUCKET=your_s3_bucket
S3_REGION=your_s3_region

# 部署脚本
#!/bin/bash
# deploy.sh

set -e

echo "🚀 开始部署采购平台..."

# 检查环境变量
if [ ! -f .env ]; then
    echo "❌ 错误: .env 文件不存在，请复制 .env.example 并配置"
    exit 1
fi

# 加载环境变量
source .env

# 检查必要的环境变量
required_vars=("DB_PASSWORD" "REDIS_PASSWORD" "JWT_SECRET" "GRAFANA_PASSWORD")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ 错误: 环境变量 $var 未设置"
        exit 1
    fi
done

# 创建必要的目录
mkdir -p logs nginx/logs monitoring/grafana/dashboards monitoring/grafana/datasources

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose down

# 拉取最新镜像
echo "📥 拉取最新镜像..."
docker-compose pull

# 构建应用镜像
echo "🔨 构建应用镜像..."
docker-compose build --no-cache

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 健康检查
echo "🔍 执行健康检查..."
services=("backend" "frontend" "postgres" "redis")
for service in "${services[@]}"; do
    if docker-compose ps $service | grep -q "Up (healthy)"; then
        echo "✅ $service 服务健康"
    else
        echo "❌ $service 服务异常"
        docker-compose logs $service
        exit 1
    fi
done

echo "🎉 部署完成！"
echo "📊 监控面板: http://localhost:3000 (admin/${GRAFANA_PASSWORD})"
echo "🔧 应用管理: http://localhost:8080/actuator"
echo "📈 指标监控: http://localhost:9090"
```

## 3. 监控和日志系统

### 3.1 Prometheus监控配置
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # 应用监控
  - job_name: 'procurement-backend'
    static_configs:
      - targets: ['backend:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 15s

  # 系统监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # 数据库监控
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Redis监控
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']

  # Nginx监控
  - job_name: 'nginx-exporter'
    static_configs:
      - targets: ['nginx-exporter:9113']
```

### 3.2 Grafana仪表板配置
```json
{
  "dashboard": {
    "id": null,
    "title": "采购平台监控仪表板",
    "tags": ["procurement", "monitoring"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "应用健康状态",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"procurement-backend\"}",
            "legendFormat": "后端服务"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "green", "value": 1}
              ]
            }
          }
        }
      },
      {
        "id": 2,
        "title": "请求响应时间",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      },
      {
        "id": 3,
        "title": "请求量",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{uri}}"
          }
        ]
      },
      {
        "id": 4,
        "title": "错误率",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"4..|5..\"}[5m]) / rate(http_requests_total[5m])",
            "legendFormat": "错误率"
          }
        ]
      },
      {
        "id": 5,
        "title": "数据库连接池",
        "type": "graph",
        "targets": [
          {
            "expr": "hikaricp_connections_active",
            "legendFormat": "活跃连接"
          },
          {
            "expr": "hikaricp_connections_idle",
            "legendFormat": "空闲连接"
          }
        ]
      },
      {
        "id": 6,
        "title": "JVM内存使用",
        "type": "graph",
        "targets": [
          {
            "expr": "jvm_memory_used_bytes{area=\"heap\"}",
            "legendFormat": "堆内存使用"
          },
          {
            "expr": "jvm_memory_max_bytes{area=\"heap\"}",
            "legendFormat": "堆内存最大值"
          }
        ]
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "5s"
  }
}
```

### 3.3 日志管理配置
```yaml
# 应用日志配置 - logback-spring.xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <message/>
                <mdc/>
                <arguments/>
                <stackTrace/>
            </providers>
        </encoder>
    </appender>
    
    <!-- 文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/application.log</file>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <message/>
                <mdc/>
                <arguments/>
                <stackTrace/>
            </providers>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/application.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 错误日志单独输出 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/error.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <message/>
                <mdc/>
                <arguments/>
                <stackTrace/>
            </providers>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
    </appender>
    
    <!-- 业务日志 -->
    <appender name="BUSINESS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/business.log</file>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <message/>
                <mdc/>
                <arguments/>
            </providers>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/business.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
    </appender>
    
    <!-- 日志级别配置 -->
    <logger name="com.procurement.platform" level="INFO"/>
    <logger name="com.procurement.platform.business" level="INFO" additivity="false">
        <appender-ref ref="BUSINESS_FILE"/>
    </logger>
    
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </root>
    
    <!-- 开发环境配置 -->
    <springProfile name="dev">
        <logger name="com.procurement.platform" level="DEBUG"/>
        <logger name="org.springframework.web" level="DEBUG"/>
    </springProfile>
    
    <!-- 生产环境配置 -->
    <springProfile name="prod">
        <logger name="com.procurement.platform" level="WARN"/>
        <logger name="org.springframework" level="WARN"/>
        <logger name="org.hibernate" level="WARN"/>
    </springProfile>
</configuration>
```

## 4. 备份和恢复策略

### 4.1 数据库备份脚本
```bash
#!/bin/bash
# backup.sh

set -e

# 配置
BACKUP_DIR="/backup"
DB_NAME="procurement_db"
DB_USER="procurement_user"
DB_HOST="localhost"
DB_PORT="5432"
RETENTION_DAYS=30

# 创建备份目录
mkdir -p $BACKUP_DIR

# 生成备份文件名
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/${DB_NAME}_backup_$TIMESTAMP.sql.gz"

echo "🗄️ 开始数据库备份..."

# 执行备份
PGPASSWORD=$DB_PASSWORD pg_dump \
    -h $DB_HOST \
    -p $DB_PORT \
    -U $DB_USER \
    -d $DB_NAME \
    --verbose \
    --no-owner \
    --no-privileges \
    | gzip > $BACKUP_FILE

# 检查备份文件
if [ -f "$BACKUP_FILE" ] && [ -s "$BACKUP_FILE" ]; then
    echo "✅ 备份成功: $BACKUP_FILE"
    echo "📊 备份大小: $(du -h $BACKUP_FILE | cut -f1)"
else
    echo "❌ 备份失败"
    exit 1
fi

# 清理旧备份
echo "🧹 清理 $RETENTION_DAYS 天前的备份..."
find $BACKUP_DIR -name "${DB_NAME}_backup_*.sql.gz" -mtime +$RETENTION_DAYS -delete

# 上传到云存储（可选）
if [ ! -z "$S3_BUCKET" ]; then
    echo "☁️ 上传备份到云存储..."
    aws s3 cp $BACKUP_FILE s3://$S3_BUCKET/backups/
fi

echo "🎉 备份完成"
```

### 4.2 恢复脚本
```bash
#!/bin/bash
# restore.sh

set -e

if [ $# -eq 0 ]; then
    echo "用法: $0 <backup_file>"
    echo "示例: $0 /backup/procurement_db_backup_20240101_120000.sql.gz"
    exit 1
fi

BACKUP_FILE=$1
DB_NAME="procurement_db"
DB_USER="procurement_user"
DB_HOST="localhost"
DB_PORT="5432"

# 检查备份文件
if [ ! -f "$BACKUP_FILE" ]; then
    echo "❌ 备份文件不存在: $BACKUP_FILE"
    exit 1
fi

echo "⚠️  警告: 此操作将完全替换现有数据库!"
read -p "确认继续? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "操作已取消"
    exit 0
fi

echo "🗄️ 开始数据库恢复..."

# 停止应用服务
echo "🛑 停止应用服务..."
docker-compose stop backend

# 删除现有数据库
echo "🗑️ 删除现有数据库..."
PGPASSWORD=$DB_PASSWORD dropdb \
    -h $DB_HOST \
    -p $DB_PORT \
    -U $DB_USER \
    --if-exists \
    $DB_NAME

# 创建新数据库
echo "🆕 创建新数据库..."
PGPASSWORD=$DB_PASSWORD createdb \
    -h $DB_HOST \
    -p $DB_PORT \
    -U $DB_USER \
    $DB_NAME

# 恢复数据
echo "📥 恢复数据..."
if [[ $BACKUP_FILE == *.gz ]]; then
    gunzip -c $BACKUP_FILE | PGPASSWORD=$DB_PASSWORD psql \
        -h $DB_HOST \
        -p $DB_PORT \
        -U $DB_USER \
        -d $DB_NAME
else
    PGPASSWORD=$DB_PASSWORD psql \
        -h $DB_HOST \
        -p $DB_PORT \
        -U $DB_USER \
        -d $DB_NAME \
        -f $BACKUP_FILE
fi

# 重启应用服务
echo "🚀 重启应用服务..."
docker-compose start backend

echo "🎉 恢复完成"
```

## 5. 性能优化和扩展

### 5.1 应用性能优化
```yaml
# 应用配置优化 - application-prod.yml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
  
  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        jdbc:
          batch_size: 25
          fetch_size: 50
        order_inserts: true
        order_updates: true
        jdbc.batch_versioned_data: true
        cache:
          use_second_level_cache: true
          use_query_cache: true
          region.factory_class: org.hibernate.cache.jcache.JCacheRegionFactory
  
  cache:
    type: redis
    redis:
      time-to-live: 600000
      cache-null-values: false
  
  redis:
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 8
        min-idle: 2

server:
  tomcat:
    threads:
      max: 200
      min-spare: 10
    connection-timeout: 20000
    max-connections: 8192
    accept-count: 100

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
```

### 5.2 负载均衡配置
```nginx
# nginx/nginx.conf
upstream backend {
    least_conn;
    server backend-1:8080 max_fails=3 fail_timeout=30s;
    server backend-2:8080 max_fails=3 fail_timeout=30s;
    server backend-3:8080 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name procurement-platform.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name procurement-platform.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    
    # 静态资源
    location /static/ {
        alias /usr/share/nginx/html/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        gzip_static on;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # 前端应用
    location / {
        try_files $uri $uri/ /index.html;
        expires 1h;
        add_header Cache-Control "public";
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

这套部署运维指南为超级个人全栈开发者提供了从单机部署到集群扩展的完整解决方案，通过Docker容器化、自动化监控、完善的备份恢复策略，确保了系统的稳定性和可扩展性。
