# 超级个人全栈开发 - 优化DDD架构设计文档

## 1. 优化DDD架构总览（7大业务域，17个限界上下文）

### 1.1 架构设计理念
- **领域驱动**：以业务领域为核心，技术服务于业务
- **测试驱动**：TDD三步法（红-绿-重构）驱动架构演进
- **全栈一体化**：前后端共享领域模型，保持一致性
- **单体架构**：17个限界上下文在单体应用中模块化实现
- **事件驱动**：通过领域事件实现上下文间解耦
- **长远演进**：架构设计考虑未来5-10年业务发展
- **超级个人优化**：架构设计考虑个人开发特点和效率
- **质量内建**：通过TDD确保架构设计的正确性和可维护性

### 1.2 优化架构图

```mermaid
graph TB
    subgraph "前端层 (Vue + TypeScript)"
        WEB[Web应用<br/>Vue 3 + TypeScript]
        MOBILE[移动端<br/>响应式设计]
        ADMIN[管理后台<br/>Element Plus]
    end

    subgraph "API网关层"
        GATEWAY[Spring Boot Gateway<br/>统一API入口]
        AUTH[认证授权<br/>JWT + OAuth2]
        RATE[限流熔断<br/>Resilience4j]
    end
    
    subgraph "应用服务层 (Application Layer)"
        subgraph "交易前置域应用服务"
            REQ_APP[需求管理应用服务]
            SUP_APP[供应商发现应用服务]
            BID_APP[竞价评估应用服务]
        end
        
        subgraph "交易执行域应用服务"
            ORD_APP[订单履约应用服务]
            LOG_APP[物流服务应用服务]
            PAY_APP[支付结算应用服务]
        end
        
        subgraph "交易后置域应用服务"
            INV_APP[库存运营应用服务]
            REL_APP[供应商关系应用服务]
            REP_APP[智能补货应用服务]
        end
        
        subgraph "用户服务域应用服务 (重构优化)"
            USR_INS_APP[用户洞察应用服务]
            USR_GRW_APP[用户增长应用服务]
        end

        subgraph "数据智能域应用服务 (扩展优化)"
            ANA_APP[数据分析应用服务]
            REC_APP[智能推荐应用服务]
            DEC_APP[智能决策应用服务]
        end

        subgraph "平台服务域应用服务"
            IAM_APP[身份权限应用服务]
            COM_APP[通信协作应用服务]
        end

        subgraph "治理风控域应用服务 (新增)"
            COMP_APP[合规管理应用服务]
            RISK_APP[风险控制应用服务]
        end
    end
    
    subgraph "领域层 (Domain Layer)"
        subgraph "交易前置域"
            REQ_DOM[需求管理领域]
            SUP_DOM[供应商发现领域]
            BID_DOM[竞价评估领域]
        end
        
        subgraph "交易执行域"
            ORD_DOM[订单履约领域]
            LOG_DOM[物流服务领域]
            PAY_DOM[支付结算领域]
        end
        
        subgraph "交易后置域"
            INV_DOM[库存运营领域]
            REL_DOM[供应商关系领域]
            REP_DOM[智能补货领域]
        end
        
        subgraph "用户服务域 (重构优化)"
            USR_INS_DOM[用户洞察领域]
            USR_GRW_DOM[用户增长领域]
        end

        subgraph "数据智能域 (扩展优化)"
            ANA_DOM[数据分析领域]
            REC_DOM[智能推荐领域]
            DEC_DOM[智能决策领域]
        end

        subgraph "平台服务域"
            IAM_DOM[身份权限领域]
            COM_DOM[通信协作领域]
        end

        subgraph "治理风控域 (新增)"
            COMP_DOM[合规管理领域]
            RISK_DOM[风险控制领域]
        end
    end
    
    subgraph "基础设施层 (Infrastructure Layer)"
        DB[(PostgreSQL<br/>统一数据库)]
        CACHE[(Redis<br/>缓存)]
        MQ[Spring Events<br/>内存事件总线]
        FILE[文件存储<br/>MinIO/云存储]
        SEARCH[Elasticsearch<br/>搜索引擎]
    end
    
    WEB --> GATEWAY
    MOBILE --> GATEWAY
    ADMIN --> GATEWAY
    
    GATEWAY --> REQ_APP
    GATEWAY --> SUP_APP
    GATEWAY --> BID_APP
    GATEWAY --> ORD_APP
    GATEWAY --> LOG_APP
    GATEWAY --> PAY_APP
    GATEWAY --> INV_APP
    GATEWAY --> REL_APP
    GATEWAY --> REP_APP
    GATEWAY --> USR_INS_APP
    GATEWAY --> USR_GRW_APP
    GATEWAY --> ANA_APP
    GATEWAY --> REC_APP
    GATEWAY --> DEC_APP
    GATEWAY --> IAM_APP
    GATEWAY --> COM_APP
    GATEWAY --> COMP_APP
    GATEWAY --> RISK_APP
    
    REQ_APP --> REQ_DOM
    SUP_APP --> SUP_DOM
    BID_APP --> BID_DOM
    ORD_APP --> ORD_DOM
    LOG_APP --> LOG_DOM
    PAY_APP --> PAY_DOM
    INV_APP --> INV_DOM
    REL_APP --> REL_DOM
    REP_APP --> REP_DOM
    USR_INS_APP --> USR_INS_DOM
    USR_GRW_APP --> USR_GRW_DOM
    ANA_APP --> ANA_DOM
    REC_APP --> REC_DOM
    DEC_APP --> DEC_DOM
    IAM_APP --> IAM_DOM
    COM_APP --> COM_DOM
    COMP_APP --> COMP_DOM
    RISK_APP --> RISK_DOM
    
    REQ_DOM --> DB
    SUP_DOM --> DB
    BID_DOM --> DB
    ORD_DOM --> DB
    LOG_DOM --> DB
    PAY_DOM --> DB
    INV_DOM --> DB
    REL_DOM --> DB
    REP_DOM --> DB
    USR_INS_DOM --> DB
    USR_GRW_DOM --> DB
    ANA_DOM --> DB
    REC_DOM --> DB
    DEC_DOM --> DB
    IAM_DOM --> DB
    COM_DOM --> DB
    COMP_DOM --> DB
    RISK_DOM --> DB
    
    REQ_DOM --> MQ
    SUP_DOM --> MQ
    BID_DOM --> MQ
    ORD_DOM --> MQ
    LOG_DOM --> MQ
    PAY_DOM --> MQ
    INV_DOM --> MQ
    REL_DOM --> MQ
    REP_DOM --> MQ
    USR_INS_DOM --> MQ
    USR_GRW_DOM --> MQ
    ANA_DOM --> MQ
    REC_DOM --> MQ
    DEC_DOM --> MQ
    IAM_DOM --> MQ
    COM_DOM --> MQ
    COMP_DOM --> MQ
    RISK_DOM --> MQ
```

## 2. 17个限界上下文详细设计（7大业务域）

### 2.1 交易前置域 (Pre-Transaction Domain)

#### 需求管理上下文 (Requirement Management Context)
```kotlin
// 聚合根：采购需求
@Entity
@Table(schema = "requirement_mgmt", name = "procurement_requirements")
class ProcurementRequirement(
    @EmbeddedId val id: RequirementId,
    val buyerId: UserId,
    var title: String,
    var description: String,
    var category: RequirementCategory,
    @ElementCollection val specifications: MutableSet<Specification>,
    @Embedded var budget: Budget,
    var deadline: LocalDate?,
    @Enumerated(EnumType.STRING) var status: RequirementStatus,
    @ElementCollection val attachments: MutableList<Attachment>,
    val createdAt: Instant,
    var updatedAt: Instant
) {
    fun publish(): RequirementPublishedEvent {
        require(status == RequirementStatus.DRAFT) { "只能发布草稿状态的需求" }
        status = RequirementStatus.PUBLISHED
        updatedAt = Instant.now()
        return RequirementPublishedEvent(id.value, buyerId.value, category, budget)
    }
    
    fun addSpecification(spec: Specification): SpecificationAddedEvent {
        specifications.add(spec)
        updatedAt = Instant.now()
        return SpecificationAddedEvent(id.value, spec)
    }
}

// 值对象：预算
@Embeddable
data class Budget(
    val minAmount: BigDecimal?,
    val maxAmount: BigDecimal?,
    val currency: Currency
) {
    init {
        if (minAmount != null && maxAmount != null) {
            require(minAmount <= maxAmount) { "最小预算不能大于最大预算" }
        }
    }
}

// 领域服务：需求匹配服务
@Service
class RequirementMatchingDomainService {
    fun findMatchingSuppliers(requirement: ProcurementRequirement): List<SupplierId> {
        // 基于需求特征匹配供应商的复杂算法
        return emptyList() // 实现省略
    }
}
```

#### 供应商发现上下文 (Supplier Discovery Context)
```kotlin
// 聚合根：供应商能力画像
@Entity
@Table(schema = "supplier_discovery", name = "supplier_capabilities")
class SupplierCapability(
    @EmbeddedId val id: SupplierCapabilityId,
    val supplierId: UserId,
    @ElementCollection val categories: MutableSet<ProductCategory>,
    @ElementCollection val certifications: MutableSet<Certification>,
    @Embedded var capacity: ProductionCapacity,
    @ElementCollection val regions: MutableSet<ServiceRegion>,
    @Embedded var qualityScore: QualityScore,
    var lastUpdated: Instant
) {
    fun updateCapacity(newCapacity: ProductionCapacity): CapacityUpdatedEvent {
        capacity = newCapacity
        lastUpdated = Instant.now()
        return CapacityUpdatedEvent(supplierId.value, newCapacity)
    }
    
    fun addCertification(cert: Certification): CertificationAddedEvent {
        certifications.add(cert)
        lastUpdated = Instant.now()
        return CertificationAddedEvent(supplierId.value, cert)
    }
}

// 领域服务：智能匹配服务
@Service
class IntelligentMatchingDomainService {
    fun calculateMatchScore(
        requirement: ProcurementRequirement,
        capability: SupplierCapability
    ): MatchScore {
        // 复杂的匹配算法实现
        return MatchScore(BigDecimal.ZERO) // 实现省略
    }
}
```

#### 竞价评估上下文 (Bidding Evaluation Context)
```kotlin
// 聚合根：竞价
@Entity
@Table(schema = "bidding_evaluation", name = "bids")
class Bid(
    @EmbeddedId val id: BidId,
    val requirementId: RequirementId,
    val supplierId: UserId,
    @Embedded val pricing: BidPricing,
    @Embedded val delivery: DeliveryTerms,
    @ElementCollection val qualityAssurances: MutableSet<QualityAssurance>,
    @Enumerated(EnumType.STRING) var status: BidStatus,
    val submittedAt: Instant,
    var evaluatedAt: Instant?
) {
    fun evaluate(evaluationResult: EvaluationResult): BidEvaluatedEvent {
        require(status == BidStatus.SUBMITTED) { "只能评估已提交的竞价" }
        status = if (evaluationResult.passed) BidStatus.QUALIFIED else BidStatus.DISQUALIFIED
        evaluatedAt = Instant.now()
        return BidEvaluatedEvent(id.value, supplierId.value, evaluationResult)
    }
}

// 领域服务：智能评标服务
@Service
class IntelligentEvaluationDomainService {
    fun evaluateBid(bid: Bid, requirement: ProcurementRequirement): EvaluationResult {
        // 多维度评标算法
        val priceScore = calculatePriceScore(bid.pricing, requirement.budget)
        val qualityScore = calculateQualityScore(bid.qualityAssurances)
        val deliveryScore = calculateDeliveryScore(bid.delivery, requirement.deadline)
        
        return EvaluationResult(
            totalScore = (priceScore + qualityScore + deliveryScore) / 3,
            passed = (priceScore + qualityScore + deliveryScore) / 3 >= BigDecimal("70")
        )
    }
}
```

### 2.2 交易执行域 (Transaction Execution Domain)

#### 订单履约上下文 (Order Fulfillment Context)
```kotlin
// 聚合根：订单
@Entity
@Table(schema = "order_fulfillment", name = "orders")
class Order(
    @EmbeddedId val id: OrderId,
    val orderNumber: String,
    val requirementId: RequirementId,
    val bidId: BidId,
    val buyerId: UserId,
    val supplierId: UserId,
    @OneToMany(cascade = [CascadeType.ALL])
    val items: MutableList<OrderItem>,
    @Embedded val totalAmount: Money,
    @Embedded val deliveryTerms: DeliveryTerms,
    @Enumerated(EnumType.STRING) var status: OrderStatus,
    val createdAt: Instant,
    var confirmedAt: Instant?
) {
    fun confirm(): OrderConfirmedEvent {
        require(status == OrderStatus.PENDING) { "只能确认待处理的订单" }
        status = OrderStatus.CONFIRMED
        confirmedAt = Instant.now()
        return OrderConfirmedEvent(id.value, buyerId.value, supplierId.value, totalAmount)
    }
    
    fun startProduction(): ProductionStartedEvent {
        require(status == OrderStatus.CONFIRMED) { "只能对已确认的订单开始生产" }
        status = OrderStatus.IN_PRODUCTION
        return ProductionStartedEvent(id.value, supplierId.value)
    }
}
```

### 2.3 交易后置域 (Post-Transaction Domain)

#### 智能补货上下文 (Intelligent Replenishment Context)
```kotlin
// 聚合根：补货策略
@Entity
@Table(schema = "intelligent_replenishment", name = "replenishment_strategies")
class ReplenishmentStrategy(
    @EmbeddedId val id: ReplenishmentStrategyId,
    val buyerId: UserId,
    val productCode: String,
    @ElementCollection val trustedSuppliers: MutableList<TrustedSupplier>,
    @Embedded var replenishmentRules: ReplenishmentRules,
    @Embedded var performanceMetrics: PerformanceMetrics,
    var lastExecuted: Instant?
) {
    fun executeOneClickReplenishment(
        quantity: Int,
        maxUnitPrice: Money
    ): OneClickReplenishmentExecutedEvent {
        val bestSupplier = selectBestTrustedSupplier()
        require(bestSupplier != null) { "没有可用的信任供应商" }
        
        lastExecuted = Instant.now()
        return OneClickReplenishmentExecutedEvent(
            strategyId = id.value,
            buyerId = buyerId.value,
            supplierId = bestSupplier.supplierId.value,
            productCode = productCode,
            quantity = quantity,
            maxUnitPrice = maxUnitPrice
        )
    }
    
    private fun selectBestTrustedSupplier(): TrustedSupplier? {
        return trustedSuppliers
            .filter { it.trustScore >= BigDecimal("0.7") }
            .maxByOrNull { it.trustScore }
    }
}

// 领域服务：智能补货算法服务
@Service
class IntelligentReplenishmentAlgorithmService {
    fun calculateOptimalQuantity(
        historicalConsumption: List<ConsumptionRecord>,
        currentStock: Int,
        safetyStock: Int
    ): Int {
        // 基于历史消耗模式和机器学习的最优补货数量计算
        val averageMonthlyConsumption = historicalConsumption
            .takeLast(12)
            .map { it.quantity }
            .average()
        
        val leadTimeDays = 30
        val safetyFactor = 1.5
        
        return ((averageMonthlyConsumption * leadTimeDays / 30) * safetyFactor).toInt()
    }
}
```

## 3. 全栈事件驱动架构

### 3.1 领域事件设计
```kotlin
// 事件基类
abstract class DomainEvent(
    val eventId: UUID = UUID.randomUUID(),
    val occurredOn: Instant = Instant.now(),
    val aggregateId: String,
    val aggregateType: String,
    val version: Int = 1
)

// 具体事件示例
data class RequirementPublishedEvent(
    val requirementId: UUID,
    val buyerId: UUID,
    val category: RequirementCategory,
    val budget: Budget
) : DomainEvent(
    aggregateId = requirementId.toString(),
    aggregateType = "ProcurementRequirement"
)

data class OrderConfirmedEvent(
    val orderId: UUID,
    val buyerId: UUID,
    val supplierId: UUID,
    val totalAmount: Money
) : DomainEvent(
    aggregateId = orderId.toString(),
    aggregateType = "Order"
)
```

### 3.2 事件处理器
```kotlin
// 跨上下文事件处理
@Component
class CrossContextEventHandler(
    private val supplierDiscoveryService: SupplierDiscoveryApplicationService,
    private val inventoryService: InventoryApplicationService,
    private val userBehaviorService: UserBehaviorApplicationService
) {
    
    @EventListener
    @Async
    fun handleRequirementPublished(event: RequirementPublishedEvent) {
        // 触发供应商匹配
        supplierDiscoveryService.findMatchingSuppliers(
            FindMatchingSuppliersCommand(
                requirementId = event.requirementId,
                category = event.category,
                budget = event.budget
            )
        )
        
        // 记录用户行为
        userBehaviorService.recordBehavior(
            RecordBehaviorCommand(
                userId = event.buyerId,
                behaviorType = BehaviorType.PUBLISH_REQUIREMENT,
                entityId = event.requirementId
            )
        )
    }
    
    @EventListener
    @Async
    fun handleOrderConfirmed(event: OrderConfirmedEvent) {
        // 更新库存预留
        inventoryService.reserveInventory(
            ReserveInventoryCommand(
                orderId = event.orderId,
                buyerId = event.buyerId
            )
        )
        
        // 记录交易行为
        userBehaviorService.recordBehavior(
            RecordBehaviorCommand(
                userId = event.buyerId,
                behaviorType = BehaviorType.CONFIRM_ORDER,
                entityId = event.orderId
            )
        )
    }
}
```

## 4. 前后端共享领域模型

### 4.1 TypeScript领域模型
```typescript
// 前端领域模型（与后端保持一致）
export interface ProcurementRequirement {
  id: string;
  buyerId: string;
  title: string;
  description: string;
  category: RequirementCategory;
  specifications: Specification[];
  budget: Budget;
  deadline?: string;
  status: RequirementStatus;
  attachments: Attachment[];
  createdAt: string;
  updatedAt: string;
}

export interface Budget {
  minAmount?: number;
  maxAmount?: number;
  currency: Currency;
}

export enum RequirementStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  IN_BIDDING = 'IN_BIDDING',
  CLOSED = 'CLOSED',
  CANCELLED = 'CANCELLED'
}

// 前端领域服务
export class RequirementDomainService {
  static canPublish(requirement: ProcurementRequirement): boolean {
    return requirement.status === RequirementStatus.DRAFT &&
           requirement.title.trim().length > 0 &&
           requirement.description.trim().length > 0;
  }
  
  static calculateBudgetRange(budget: Budget): string {
    if (budget.minAmount && budget.maxAmount) {
      return `${budget.minAmount} - ${budget.maxAmount} ${budget.currency}`;
    } else if (budget.maxAmount) {
      return `≤ ${budget.maxAmount} ${budget.currency}`;
    } else {
      return '面议';
    }
  }
}
```

### 4.2 前端状态管理
```typescript
// Pinia状态管理
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

// 异步操作
export const publishRequirement = createAsyncThunk(
  'requirements/publish',
  async (requirementId: string, { rejectWithValue }) => {
    try {
      const response = await requirementApi.publish(requirementId);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

// 状态切片
const requirementSlice = createSlice({
  name: 'requirements',
  initialState: {
    items: [] as ProcurementRequirement[],
    loading: false,
    error: null as string | null
  },
  reducers: {
    addRequirement: (state, action) => {
      state.items.push(action.payload);
    },
    updateRequirement: (state, action) => {
      const index = state.items.findIndex(item => item.id === action.payload.id);
      if (index !== -1) {
        state.items[index] = action.payload;
      }
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(publishRequirement.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(publishRequirement.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.items.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.items[index] = action.payload;
        }
      })
      .addCase(publishRequirement.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});
```

## 5. 超级个人开发优化

### 5.1 代码生成策略
```kotlin
// 使用注解驱动的代码生成
@GenerateApplicationService
@GenerateRepository
@GenerateDto
@Entity
@Table(schema = "requirement_mgmt", name = "procurement_requirements")
class ProcurementRequirement {
    // 实体定义
}

// 自动生成的应用服务
@Service
@Generated
class ProcurementRequirementApplicationService(
    private val repository: ProcurementRequirementRepository,
    private val eventPublisher: DomainEventPublisher
) {
    // 自动生成的CRUD方法
}
```

### 5.2 测试策略
```kotlin
// 领域测试
@ExtendWith(MockKExtension::class)
class ProcurementRequirementTest {
    
    @Test
    fun `should publish requirement when status is draft`() {
        // Given
        val requirement = ProcurementRequirement(
            id = RequirementId.generate(),
            buyerId = UserId.generate(),
            title = "测试需求",
            description = "测试描述",
            category = RequirementCategory.ELECTRONICS,
            specifications = mutableSetOf(),
            budget = Budget(BigDecimal("1000"), BigDecimal("2000"), Currency.CNY),
            deadline = LocalDate.now().plusDays(30),
            status = RequirementStatus.DRAFT,
            attachments = mutableListOf(),
            createdAt = Instant.now(),
            updatedAt = Instant.now()
        )
        
        // When
        val event = requirement.publish()
        
        // Then
        assertThat(requirement.status).isEqualTo(RequirementStatus.PUBLISHED)
        assertThat(event).isInstanceOf(RequirementPublishedEvent::class.java)
    }
}
```

## 5. 优化后的用户服务域设计

### 5.1 用户洞察上下文 (User Insights Context)
```kotlin
// 聚合根：用户洞察档案
@Entity
@Table(schema = "user_insights", name = "user_insight_profiles")
class UserInsightProfile(
    @EmbeddedId val id: UserInsightProfileId,
    val userId: UserId,
    @Embedded var behaviorMetrics: BehaviorMetrics,
    @Embedded var userProfile: UserProfile,
    @ElementCollection val behaviorPatterns: MutableSet<BehaviorPattern>,
    @ElementCollection val userTags: MutableSet<UserTag>,
    val createdAt: Instant,
    var updatedAt: Instant
) {
    fun updateBehaviorMetrics(newMetrics: BehaviorMetrics): BehaviorMetricsUpdatedEvent {
        behaviorMetrics = newMetrics
        updatedAt = Instant.now()
        return BehaviorMetricsUpdatedEvent(userId.value, newMetrics)
    }

    fun addBehaviorPattern(pattern: BehaviorPattern): BehaviorPatternAddedEvent {
        behaviorPatterns.add(pattern)
        updatedAt = Instant.now()
        return BehaviorPatternAddedEvent(userId.value, pattern)
    }

    fun updateUserTags(tags: Set<UserTag>): UserTagsUpdatedEvent {
        userTags.clear()
        userTags.addAll(tags)
        updatedAt = Instant.now()
        return UserTagsUpdatedEvent(userId.value, tags)
    }
}

// 值对象：行为指标
@Embeddable
data class BehaviorMetrics(
    val totalSessions: Int,
    val averageSessionDuration: Duration,
    val pageViews: Int,
    val clickThroughRate: BigDecimal,
    val conversionRate: BigDecimal,
    val lastActiveTime: Instant
)

// 值对象：用户画像
@Embeddable
data class UserProfile(
    val industry: String?,
    val companySize: CompanySize?,
    val purchasingPower: PurchasingPower,
    val preferredCategories: Set<String>,
    val riskTolerance: RiskTolerance
)
```

### 5.2 用户增长上下文 (User Growth Context)
```kotlin
// 聚合根：用户增长策略
@Entity
@Table(schema = "user_growth", name = "user_growth_strategies")
class UserGrowthStrategy(
    @EmbeddedId val id: UserGrowthStrategyId,
    val userId: UserId,
    @Embedded var engagementLevel: EngagementLevel,
    @Embedded var incentiveProfile: IncentiveProfile,
    @ElementCollection val growthActivities: MutableList<GrowthActivity>,
    @ElementCollection val referralRelations: MutableSet<ReferralRelation>,
    val createdAt: Instant,
    var updatedAt: Instant
) {
    fun executeGrowthActivity(activity: GrowthActivity): GrowthActivityExecutedEvent {
        growthActivities.add(activity)
        engagementLevel = engagementLevel.increaseBy(activity.engagementPoints)
        updatedAt = Instant.now()
        return GrowthActivityExecutedEvent(userId.value, activity)
    }

    fun addReferral(referredUserId: UserId, incentive: Incentive): ReferralAddedEvent {
        val relation = ReferralRelation(userId, referredUserId, incentive, Instant.now())
        referralRelations.add(relation)
        incentiveProfile = incentiveProfile.addIncentive(incentive)
        updatedAt = Instant.now()
        return ReferralAddedEvent(userId.value, referredUserId.value, incentive)
    }
}
```

## 6. 扩展后的数据智能域设计

### 6.1 智能推荐上下文 (Intelligent Recommendation Context)
```kotlin
// 聚合根：推荐引擎
@Entity
@Table(schema = "intelligent_recommendation", name = "recommendation_engines")
class RecommendationEngine(
    @EmbeddedId val id: RecommendationEngineId,
    val engineType: RecommendationEngineType,
    @ElementCollection val algorithms: MutableSet<RecommendationAlgorithm>,
    @Embedded var performanceMetrics: RecommendationPerformanceMetrics,
    val createdAt: Instant,
    var updatedAt: Instant
) {
    fun generateRecommendations(
        userId: UserId,
        context: RecommendationContext
    ): RecommendationsGeneratedEvent {
        val recommendations = algorithms
            .map { it.generateRecommendations(userId, context) }
            .flatten()
            .distinctBy { it.itemId }
            .sortedByDescending { it.score }
            .take(10)

        updatedAt = Instant.now()
        return RecommendationsGeneratedEvent(userId.value, recommendations)
    }
}
```

### 6.2 智能决策上下文 (Intelligent Decision Context)
```kotlin
// 聚合根：决策引擎
@Entity
@Table(schema = "intelligent_decision", name = "decision_engines")
class DecisionEngine(
    @EmbeddedId val id: DecisionEngineId,
    val decisionType: DecisionType,
    @ElementCollection val decisionRules: MutableSet<DecisionRule>,
    @ElementCollection val mlModels: MutableSet<MLModel>,
    @Embedded var decisionMetrics: DecisionMetrics,
    val createdAt: Instant,
    var updatedAt: Instant
) {
    fun makeDecision(
        decisionContext: DecisionContext
    ): DecisionMadeEvent {
        val ruleBasedDecision = applyRules(decisionContext)
        val mlBasedDecision = applyMLModels(decisionContext)

        val finalDecision = combineDecisions(ruleBasedDecision, mlBasedDecision)

        decisionMetrics = decisionMetrics.recordDecision(finalDecision)
        updatedAt = Instant.now()

        return DecisionMadeEvent(decisionType, finalDecision, decisionContext)
    }
}
```

## 7. 新增治理风控域设计

### 7.1 合规管理上下文 (Compliance Management Context)
```kotlin
// 聚合根：合规策略
@Entity
@Table(schema = "compliance_management", name = "compliance_policies")
class CompliancePolicy(
    @EmbeddedId val id: CompliancePolicyId,
    val policyName: String,
    val regulationType: RegulationType,
    @ElementCollection val complianceRules: MutableSet<ComplianceRule>,
    @ElementCollection val auditTrails: MutableList<AuditTrail>,
    @Enumerated(EnumType.STRING) var status: CompliancePolicyStatus,
    val effectiveDate: LocalDate,
    val expiryDate: LocalDate?,
    val createdAt: Instant,
    var updatedAt: Instant
) {
    fun checkCompliance(transaction: Transaction): ComplianceCheckResult {
        val violations = complianceRules
            .mapNotNull { rule -> rule.check(transaction) }
            .toList()

        val result = if (violations.isEmpty()) {
            ComplianceCheckResult.compliant()
        } else {
            ComplianceCheckResult.nonCompliant(violations)
        }

        auditTrails.add(AuditTrail.create(transaction.id, result))
        updatedAt = Instant.now()

        return result
    }
}
```

### 7.2 风险控制上下文 (Risk Control Context)
```kotlin
// 聚合根：风险评估模型
@Entity
@Table(schema = "risk_control", name = "risk_assessment_models")
class RiskAssessmentModel(
    @EmbeddedId val id: RiskAssessmentModelId,
    val modelName: String,
    val riskType: RiskType,
    @ElementCollection val riskFactors: MutableSet<RiskFactor>,
    @ElementCollection val riskThresholds: MutableSet<RiskThreshold>,
    @Embedded var modelPerformance: ModelPerformance,
    val createdAt: Instant,
    var updatedAt: Instant
) {
    fun assessRisk(assessmentContext: RiskAssessmentContext): RiskAssessmentResult {
        val riskScore = calculateRiskScore(assessmentContext)
        val riskLevel = determineRiskLevel(riskScore)
        val recommendations = generateRecommendations(riskLevel, assessmentContext)

        val result = RiskAssessmentResult(
            riskScore = riskScore,
            riskLevel = riskLevel,
            recommendations = recommendations,
            assessedAt = Instant.now()
        )

        modelPerformance = modelPerformance.recordAssessment(result)
        updatedAt = Instant.now()

        return result
    }

    private fun calculateRiskScore(context: RiskAssessmentContext): BigDecimal {
        return riskFactors
            .map { factor -> factor.calculateScore(context) * factor.weight }
            .fold(BigDecimal.ZERO) { acc, score -> acc + score }
    }
}
```

## 8. 架构优化总结

这套优化的全栈DDD架构设计将原有的6域16上下文演进为7域17上下文，主要优化包括：

### 8.1 用户服务域重构
- **合并优化**：将用户行为+用户画像合并为用户洞察上下文，提高数据一致性
- **功能整合**：将用户参与+激励增长合并为用户增长上下文，减少跨上下文通信

### 8.2 数据智能域扩展
- **专业化分工**：将智能运营拆分为推荐、决策两个专业上下文
- **AI能力增强**：为未来AI技术发展预留独立演进空间

### 8.3 治理风控域新增
- **合规保障**：新增合规管理上下文，应对监管要求
- **风险控制**：新增风险控制上下文，保障交易安全

### 8.4 长远价值
- **架构演进**：支持未来5-10年的业务发展需求
- **技术前瞻**：为AI、大数据、区块链等新技术预留空间
- **监管适应**：满足日益严格的合规和风险控制要求

## 10. TDD驱动的架构实现

### 10.1 TDD领域模型设计

#### 步骤1：编写领域测试（RED）
```kotlin
// 先写测试，定义领域行为
class ProcurementRequirementTest {

    @Test
    fun `should create requirement with valid business rules`() {
        // Given
        val title = "采购笔记本电脑"
        val description = "需要采购100台高性能笔记本电脑"
        val budget = Money(BigDecimal("500000"), Currency.CNY)
        val deadline = LocalDate.now().plusDays(30)

        // When
        val requirement = ProcurementRequirement.create(
            id = RequirementId.generate(),
            buyerId = UserId.generate(),
            title = title,
            description = description,
            budget = budget,
            deadline = deadline
        )

        // Then
        assertThat(requirement.status).isEqualTo(RequirementStatus.DRAFT)
        assertThat(requirement.title.value).isEqualTo(title)
        assertThat(requirement.budget).isEqualTo(budget)
    }

    @Test
    fun `should publish requirement and generate domain event`() {
        // Given
        val requirement = RequirementTestDataBuilder()
            .withStatus(RequirementStatus.DRAFT)
            .build()

        // When
        val event = requirement.publish()

        // Then
        assertThat(requirement.status).isEqualTo(RequirementStatus.PUBLISHED)
        assertThat(event).isInstanceOf(RequirementPublishedEvent::class.java)
        assertThat(event.requirementId).isEqualTo(requirement.id.value)
    }

    @Test
    fun `should not allow publishing when budget exceeds approval limit without approval`() {
        // Given
        val requirement = RequirementTestDataBuilder()
            .withBudget(BigDecimal("1000000")) // 超过审批限额
            .withStatus(RequirementStatus.DRAFT)
            .withoutApproval()
            .build()

        // When & Then
        assertThrows<BusinessRuleViolationException> {
            requirement.publish()
        }
    }
}
```

#### 步骤2：实现领域模型（GREEN）
```kotlin
// 基于测试实现领域模型
@Entity
@Table(schema = "requirement_mgmt", name = "procurement_requirements")
class ProcurementRequirement private constructor(
    @EmbeddedId val id: RequirementId,
    @Embedded val buyerId: UserId,
    @Embedded var title: RequirementTitle,
    @Embedded var description: RequirementDescription,
    @Embedded var budget: Money,
    @Embedded var deadline: RequirementDeadline,
    @Enumerated(EnumType.STRING) var status: RequirementStatus,
    @ElementCollection val specifications: MutableSet<RequirementSpecification>,
    @ElementCollection val attachments: MutableList<RequirementAttachment>,
    var approvalId: ApprovalId? = null,
    val createdAt: Instant,
    var updatedAt: Instant
) {
    companion object {
        private val APPROVAL_LIMIT = Money(BigDecimal("500000"), Currency.CNY)

        fun create(
            id: RequirementId,
            buyerId: UserId,
            title: String,
            description: String,
            budget: Money,
            deadline: LocalDate
        ): ProcurementRequirement {
            return ProcurementRequirement(
                id = id,
                buyerId = buyerId,
                title = RequirementTitle.of(title),
                description = RequirementDescription.of(description),
                budget = budget,
                deadline = RequirementDeadline.of(deadline),
                status = RequirementStatus.DRAFT,
                specifications = mutableSetOf(),
                attachments = mutableListOf(),
                createdAt = Instant.now(),
                updatedAt = Instant.now()
            )
        }
    }

    fun publish(): RequirementPublishedEvent {
        require(status == RequirementStatus.DRAFT) {
            "只有草稿状态的需求才能发布"
        }

        if (budget.amount > APPROVAL_LIMIT.amount && approvalId == null) {
            throw BusinessRuleViolationException(
                "超过审批限额${APPROVAL_LIMIT.amount}的需求必须先获得审批"
            )
        }

        status = RequirementStatus.PUBLISHED
        updatedAt = Instant.now()

        return RequirementPublishedEvent(
            requirementId = id.value,
            buyerId = buyerId.value,
            title = title.value,
            budget = budget,
            publishedAt = updatedAt
        )
    }

    fun addSpecification(specification: RequirementSpecification): SpecificationAddedEvent {
        require(status == RequirementStatus.DRAFT) {
            "只有草稿状态的需求才能添加规格"
        }

        specifications.add(specification)
        updatedAt = Instant.now()

        return SpecificationAddedEvent(id.value, specification)
    }
}
```

#### 步骤3：重构优化（REFACTOR）
```kotlin
// 在测试保护下重构，提取业务规则
class ProcurementRequirement private constructor(
    // ... 属性保持不变
) {
    companion object {
        fun create(
            id: RequirementId,
            buyerId: UserId,
            title: String,
            description: String,
            budget: Money,
            deadline: LocalDate
        ): ProcurementRequirement {
            // 使用工厂方法和值对象确保数据有效性
            return ProcurementRequirement(
                id = id,
                buyerId = buyerId,
                title = RequirementTitle.of(title),
                description = RequirementDescription.of(description),
                budget = budget,
                deadline = RequirementDeadline.of(deadline),
                status = RequirementStatus.DRAFT,
                specifications = mutableSetOf(),
                attachments = mutableListOf(),
                createdAt = Instant.now(),
                updatedAt = Instant.now()
            )
        }
    }

    fun publish(): RequirementPublishedEvent {
        // 提取业务规则到专门的方法
        validateCanPublish()
        checkApprovalRequirement()

        status = RequirementStatus.PUBLISHED
        updatedAt = Instant.now()

        return RequirementPublishedEvent(
            requirementId = id.value,
            buyerId = buyerId.value,
            title = title.value,
            budget = budget,
            publishedAt = updatedAt
        )
    }

    private fun validateCanPublish() {
        require(status == RequirementStatus.DRAFT) {
            "只有草稿状态的需求才能发布"
        }
    }

    private fun checkApprovalRequirement() {
        val approvalPolicy = ApprovalPolicy.forBudget(budget)
        if (approvalPolicy.requiresApproval() && approvalId == null) {
            throw BusinessRuleViolationException(
                "预算${budget.amount}超过审批限额，需要先获得审批"
            )
        }
    }
}

// 提取审批策略
class ApprovalPolicy private constructor(
    private val budget: Money
) {
    companion object {
        private val APPROVAL_LIMIT = Money(BigDecimal("500000"), Currency.CNY)

        fun forBudget(budget: Money): ApprovalPolicy {
            return ApprovalPolicy(budget)
        }
    }

    fun requiresApproval(): Boolean {
        return budget.amount > APPROVAL_LIMIT.amount
    }
}
```

### 10.2 TDD应用服务设计

#### TDD应用服务测试
```kotlin
class RequirementApplicationServiceTest {

    private val mockRepository = mockk<RequirementRepository>()
    private val mockEventPublisher = mockk<ApplicationEventPublisher>()
    private val mockApprovalService = mockk<ApprovalService>()

    private val service = RequirementApplicationService(
        mockRepository, mockEventPublisher, mockApprovalService
    )

    @Test
    fun `should create and save requirement when valid command is provided`() {
        // Given
        val command = CreateRequirementCommand(
            buyerId = UserId.generate().value,
            title = "采购笔记本电脑",
            description = "需要采购100台笔记本电脑",
            budget = BigDecimal("500000"),
            currency = "CNY",
            deadline = LocalDate.now().plusDays(30)
        )

        every { mockRepository.save(any()) } returns Unit
        every { mockEventPublisher.publishEvent(any()) } returns Unit

        // When
        val result = service.createRequirement(command)

        // Then
        assertThat(result.isSuccess).isTrue()
        verify { mockRepository.save(any()) }
        verify { mockEventPublisher.publishEvent(any<RequirementCreatedEvent>()) }
    }

    @Test
    fun `should publish requirement and send notification when publish command is valid`() {
        // Given
        val requirementId = RequirementId.generate()
        val requirement = RequirementTestDataBuilder()
            .withId(requirementId)
            .withStatus(RequirementStatus.DRAFT)
            .build()

        every { mockRepository.findById(requirementId) } returns requirement
        every { mockRepository.save(any()) } returns Unit
        every { mockEventPublisher.publishEvent(any()) } returns Unit

        val command = PublishRequirementCommand(requirementId.value)

        // When
        val result = service.publishRequirement(command)

        // Then
        assertThat(result.isSuccess).isTrue()
        assertThat(requirement.status).isEqualTo(RequirementStatus.PUBLISHED)
        verify { mockRepository.save(requirement) }
        verify { mockEventPublisher.publishEvent(any<RequirementPublishedEvent>()) }
    }
}
```

### 10.3 TDD前端组件设计

#### Vue组件TDD测试
```typescript
// RequirementForm.test.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import RequirementForm from '@/components/RequirementForm.vue'

describe('RequirementForm', () => {
  it('should validate required fields before submission', async () => {
    // Given
    const wrapper = mount(RequirementForm)

    // When - 不填写必填字段直接提交
    await wrapper.find('[data-testid="submit-button"]').trigger('click')

    // Then - 应该显示验证错误
    expect(wrapper.find('[data-testid="title-error"]').text()).toBe('标题不能为空')
    expect(wrapper.find('[data-testid="budget-error"]').text()).toBe('预算不能为空')
    expect(wrapper.emitted('submit')).toBeFalsy()
  })

  it('should emit submit event when form is valid', async () => {
    // Given
    const wrapper = mount(RequirementForm)

    // When - 填写有效数据并提交
    await wrapper.find('[data-testid="title-input"]').setValue('采购笔记本电脑')
    await wrapper.find('[data-testid="description-input"]').setValue('需要采购100台笔记本电脑')
    await wrapper.find('[data-testid="budget-input"]').setValue('500000')
    await wrapper.find('[data-testid="submit-button"]').trigger('click')

    // Then - 应该触发提交事件
    expect(wrapper.emitted('submit')).toBeTruthy()
    const submitEvent = wrapper.emitted('submit')?.[0]?.[0] as any
    expect(submitEvent.title).toBe('采购笔记本电脑')
    expect(submitEvent.budget).toBe(500000)
  })

  it('should display loading state when form is submitting', async () => {
    // Given
    const wrapper = mount(RequirementForm, {
      props: { isSubmitting: true }
    })

    // Then
    expect(wrapper.find('[data-testid="submit-button"]').attributes('loading')).toBeDefined()
    expect(wrapper.find('[data-testid="submit-button"]').text()).toBe('提交中...')
  })
})
```

### 10.4 TDD架构演进策略

#### 渐进式TDD实施
```mermaid
graph LR
    A[第1阶段<br/>核心领域TDD] --> B[第2阶段<br/>应用服务TDD]
    B --> C[第3阶段<br/>API接口TDD]
    C --> D[第4阶段<br/>前端组件TDD]
    D --> E[第5阶段<br/>端到端TDD]

    subgraph "TDD成熟度"
        F[基础TDD<br/>单元测试]
        G[进阶TDD<br/>集成测试]
        H[高级TDD<br/>系统测试]
    end

    A --> F
    C --> G
    E --> H
```

#### TDD质量门禁
```kotlin
// 质量门禁配置
class QualityGate {
    companion object {
        const val MIN_UNIT_TEST_COVERAGE = 95
        const val MIN_INTEGRATION_TEST_COVERAGE = 85
        const val MAX_UNIT_TEST_EXECUTION_TIME = 5000 // 5秒
        const val MAX_INTEGRATION_TEST_EXECUTION_TIME = 30000 // 30秒
    }

    fun checkQuality(testResults: TestResults): QualityGateResult {
        val violations = mutableListOf<QualityViolation>()

        if (testResults.unitTestCoverage < MIN_UNIT_TEST_COVERAGE) {
            violations.add(QualityViolation.INSUFFICIENT_UNIT_TEST_COVERAGE)
        }

        if (testResults.integrationTestCoverage < MIN_INTEGRATION_TEST_COVERAGE) {
            violations.add(QualityViolation.INSUFFICIENT_INTEGRATION_TEST_COVERAGE)
        }

        if (testResults.unitTestExecutionTime > MAX_UNIT_TEST_EXECUTION_TIME) {
            violations.add(QualityViolation.SLOW_UNIT_TESTS)
        }

        return QualityGateResult(
            passed = violations.isEmpty(),
            violations = violations
        )
    }
}
```

这套TDD驱动的架构实现在保持业务完整性的基础上，通过测试先行的方式确保了架构设计的正确性和可维护性，为超级个人开发者提供了更加可靠和高效的架构基础。
