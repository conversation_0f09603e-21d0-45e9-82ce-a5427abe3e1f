# 超级个人全栈开发 - 前端架构设计文档

## 1. 前端架构总览

### 1.1 现代化前端架构理念
- **企业级标准**：采用Element Plus企业级前端解决方案
- **类型安全**：TypeScript全栈类型安全，与后端Kotlin呼应
- **组件化设计**：高度可复用的组件库和设计系统
- **状态管理**：Pinia现代化状态管理
- **性能优化**：代码分割、懒加载、缓存优化
- **用户体验**：响应式设计、国际化、无障碍访问

### 1.2 前端技术栈架构图
```mermaid
graph TB
    subgraph "用户界面层 (UI Layer)"
        WEB[Web应用<br/>Vue 3]
        MOBILE[移动端<br/>响应式设计]
        PWA[PWA应用<br/>离线支持]
    end

    subgraph "组件层 (Component Layer)"
        LAYOUT[布局组件<br/>Header/Sidebar/Footer]
        BUSINESS[业务组件<br/>需求卡片/供应商档案]
        COMMON[通用组件<br/>表格/表单/图表]
        UI[UI组件库<br/>Element Plus]
    end
    
    subgraph "页面层 (Page Layer)"
        REQ_PAGES[需求管理页面<br/>列表/详情/创建]
        SUP_PAGES[供应商页面<br/>发现/档案/评价]
        ORD_PAGES[订单页面<br/>履约/跟踪/确认]
        ANA_PAGES[分析页面<br/>报表/仪表板]
    end
    
    subgraph "状态管理层 (State Layer)"
        PINIA[Pinia<br/>全局状态]
        COMPOSABLES[Composables<br/>组合式API]
        LOCAL[Local State<br/>组件状态]
        CACHE[缓存管理<br/>数据缓存]
    end
    
    subgraph "服务层 (Service Layer)"
        API[API客户端<br/>Axios + 拦截器]
        AUTH[认证服务<br/>JWT + 权限]
        WS[WebSocket<br/>实时通信]
        UPLOAD[文件上传<br/>多媒体处理]
    end
    
    subgraph "工具层 (Utility Layer)"
        UTILS[工具函数<br/>格式化/验证]
        HOOKS[组合式函数<br/>业务逻辑复用]
        TYPES[TypeScript类型<br/>类型定义]
        CONSTANTS[常量定义<br/>配置管理]
    end
    
    WEB --> LAYOUT
    MOBILE --> LAYOUT
    PWA --> LAYOUT
    
    LAYOUT --> BUSINESS
    LAYOUT --> COMMON
    BUSINESS --> UI
    COMMON --> UI
    
    REQ_PAGES --> BUSINESS
    SUP_PAGES --> BUSINESS
    ORD_PAGES --> BUSINESS
    ANA_PAGES --> BUSINESS
    
    BUSINESS --> PINIA
    BUSINESS --> COMPOSABLES
    BUSINESS --> LOCAL

    COMPOSABLES --> API
    API --> AUTH
    API --> WS
    
    BUSINESS --> HOOKS
    HOOKS --> UTILS
    HOOKS --> TYPES
```

## 2. 核心技术架构设计

### 2.1 Vue 3 + TypeScript 架构
```typescript
// 主应用入口 - main.ts
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import 'element-plus/dist/index.css'
import App from './App.vue'
import { routes } from './router'
import { useAuthStore } from './stores/auth'
import './styles/globals.css'

const app = createApp(App)
const pinia = createPinia()
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else {
    next()
  }
})

app.use(pinia)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
})

app.mount('#app')

// 主应用组件 - App.vue
<template>
  <div id="app">
    <el-config-provider :locale="locale">
      <router-view v-if="!isLoading" />
      <PageLoading v-else />
    </el-config-provider>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import { useAuthStore } from './stores/auth'
import PageLoading from './components/common/PageLoading.vue'

const locale = zhCn
const isLoading = ref(true)
const authStore = useAuthStore()

onMounted(async () => {
  try {
    await authStore.initializeAuth()
  } finally {
    isLoading.value = false
  }
})
</script>
```

### 2.2 路由架构设计
```typescript
// 路由配置 - router/index.ts
import { RouteRecordRaw } from 'vue-router'
import { defineAsyncComponent } from 'vue'
import Layout from '@/layouts/Layout.vue'

// 懒加载页面组件
const Dashboard = defineAsyncComponent(() => import('@/pages/Dashboard.vue'))
const RequirementPages = defineAsyncComponent(() => import('@/pages/requirements/index.vue'))
const SupplierPages = defineAsyncComponent(() => import('@/pages/suppliers/index.vue'))
const OrderPages = defineAsyncComponent(() => import('@/pages/orders/index.vue'))
const InventoryPages = defineAsyncComponent(() => import('@/pages/inventory/index.vue'))
const AnalyticsPages = defineAsyncComponent(() => import('@/pages/analytics/index.vue'))
const SettingsPages = defineAsyncComponent(() => import('@/pages/settings/index.vue'))
const Login = defineAsyncComponent(() => import('@/pages/Login.vue'))
const NotFound = defineAsyncComponent(() => import('@/pages/NotFound.vue'))

export const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: {
          title: '仪表板',
          icon: 'Dashboard'
        }
      },
      {
        path: 'requirements',
        name: 'Requirements',
        component: RequirementPages,
        meta: {
          title: '需求管理',
          icon: 'Document',
          permissions: ['requirement:read']
        }
      },
      {
        path: 'suppliers',
        name: 'Suppliers',
        component: SupplierPages,
        meta: {
          title: '供应商管理',
          icon: 'OfficeBuilding',
          permissions: ['supplier:read']
        }
      },
      {
        path: 'orders',
        name: 'Orders',
        component: OrderPages,
        meta: {
          title: '订单管理',
          icon: 'ShoppingCart',
          permissions: ['order:read']
        }
      },
      {
        path: 'inventory',
        name: 'Inventory',
        component: InventoryPages,
        meta: {
          title: '库存管理',
          icon: 'Box',
          permissions: ['inventory:read']
        }
      },
      {
        path: 'analytics',
        name: 'Analytics',
        component: AnalyticsPages,
        meta: {
          title: '数据分析',
          icon: 'DataAnalysis',
          permissions: ['analytics:read']
        }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: SettingsPages,
        meta: {
          title: '系统设置',
          icon: 'Setting',
          permissions: ['system:admin']
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound
  }
]

// 权限检查工具函数
export function hasPermission(permissions: string[], userPermissions: string[]): boolean {
  if (!permissions || permissions.length === 0) return true
  return permissions.some(permission => userPermissions.includes(permission))
}
```

### 2.3 状态管理架构
```typescript
// Pinia Store 配置 - stores/index.ts
import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'

const pinia = createPinia()
pinia.use(createPersistedState())

export default pinia

// 认证状态管理 - stores/auth.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { User } from '@/types/auth'
import { authApi } from '@/services/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const isLoading = ref(false)
  const permissions = ref<string[]>([])

  // 计算属性
  const isAuthenticated = computed(() => !!user.value && !!token.value)

  const hasPermission = computed(() => (permission: string) => {
    return permissions.value.includes(permission)
  })

  const hasAnyPermission = computed(() => (perms: string[]) => {
    return perms.some(permission => permissions.value.includes(permission))
  })

  // 方法
  async function login(credentials: LoginCredentials) {
    isLoading.value = true
    try {
      const response = await authApi.login(credentials)
      setCredentials(response.user, response.token)
      return response
    } finally {
      isLoading.value = false
    }
  }

  function setCredentials(userData: User, userToken: string) {
    user.value = userData
    token.value = userToken
    permissions.value = userData.permissions || []
    localStorage.setItem('token', userToken)
  }

  function logout() {
    user.value = null
    token.value = null
    permissions.value = []
    localStorage.removeItem('token')
  }

  return {
    user, token, isLoading, permissions,
    isAuthenticated, hasPermission, hasAnyPermission,
    login, logout, setCredentials
  }
}, {
  persist: {
    key: 'auth',
    storage: localStorage,
    paths: ['user', 'token', 'permissions']
  }
})

// API 服务定义 - services/api.ts
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

class ApiService {
  private instance: AxiosInstance

  constructor() {
    this.instance = axios.create({
      baseURL: '/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const authStore = useAuthStore()
        if (authStore.token) {
          config.headers.Authorization = `Bearer ${authStore.token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        return response.data
      },
      async (error) => {
        const authStore = useAuthStore()

        if (error.response?.status === 401) {
          authStore.logout()
          ElMessage.error('登录已过期，请重新登录')
          window.location.href = '/login'
        } else if (error.response?.status >= 500) {
          ElMessage.error('服务器错误，请稍后重试')
        }

        return Promise.reject(error)
      }
    )
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.get(url, config)
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.post(url, data, config)
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.put(url, data, config)
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.delete(url, config)
  }
}

export const apiService = new ApiService()

// 需求管理API服务 - services/requirement.ts
import { apiService } from './api'
import { Requirement, CreateRequirementRequest, UpdateRequirementRequest, RequirementQuery, PaginatedResponse } from '@/types'

export const requirementService = {
  async getRequirements(params: RequirementQuery): Promise<PaginatedResponse<Requirement>> {
    return apiService.get('/requirements', { params })
  },

  async getRequirement(id: string): Promise<Requirement> {
    return apiService.get(`/requirements/${id}`)
  },

  async createRequirement(data: CreateRequirementRequest): Promise<Requirement> {
    return apiService.post('/requirements', data)
  },

  async updateRequirement(id: string, data: UpdateRequirementRequest): Promise<Requirement> {
    return apiService.put(`/requirements/${id}`, data)
  },

  async publishRequirement(id: string): Promise<Requirement> {
    return apiService.post(`/requirements/${id}/publish`)
  },

  async deleteRequirement(id: string): Promise<void> {
    return apiService.delete(`/requirements/${id}`)
  }
}

// 需求管理Composable - composables/useRequirements.ts
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { requirementService } from '@/services/requirement'
import { Requirement, RequirementQuery, CreateRequirementRequest } from '@/types'

export function useRequirements() {
  const requirements = ref<Requirement[]>([])
  const total = ref(0)
  const loading = ref(false)
  const query = reactive<RequirementQuery>({
    page: 0,
    size: 20,
    keyword: '',
    status: undefined,
    category: undefined
  })

  async function fetchRequirements() {
    loading.value = true
    try {
      const response = await requirementService.getRequirements(query)
      requirements.value = response.content
      total.value = response.totalElements
    } catch (error) {
      ElMessage.error('获取需求列表失败')
    } finally {
      loading.value = false
    }
  }

  async function createRequirement(data: CreateRequirementRequest) {
    try {
      await requirementService.createRequirement(data)
      ElMessage.success('需求创建成功')
      await fetchRequirements()
    } catch (error) {
      ElMessage.error('需求创建失败')
      throw error
    }
  }

  async function publishRequirement(id: string) {
    try {
      await requirementService.publishRequirement(id)
      ElMessage.success('需求发布成功')
      await fetchRequirements()
    } catch (error) {
      ElMessage.error('需求发布失败')
      throw error
    }
  }

  return {
    requirements,
    total,
    loading,
    query,
    fetchRequirements,
    createRequirement,
    publishRequirement
  }
}
```

## 3. 组件架构设计

### 3.1 业务组件设计
```vue
<!-- 需求卡片组件 - components/RequirementCard.vue -->
<template>
  <el-card class="requirement-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span class="title">{{ requirement.title }}</span>
        <el-tag :type="getStatusType(requirement.status)">
          {{ getStatusText(requirement.status) }}
        </el-tag>
      </div>
    </template>

    <div class="card-content">
      <p class="description">{{ requirement.description }}</p>

      <div class="meta-info">
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="meta-item">
              <span class="label">类别:</span>
              <span class="value">{{ requirement.category }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="meta-item">
              <span class="label">预算:</span>
              <span class="value">¥{{ formatBudget(requirement.budget) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="meta-item">
              <span class="label">截止日期:</span>
              <span class="value">{{ formatDate(requirement.deadline) }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer v-if="showActions">
      <div class="card-actions">
        <el-button size="small" @click="handleView">查看</el-button>
        <el-button size="small" type="primary" @click="handleEdit">编辑</el-button>
        <el-button
          v-if="requirement.status === 'DRAFT'"
          size="small"
          type="success"
          @click="handlePublish"
        >
          发布
        </el-button>
        <el-button size="small" type="danger" @click="handleDelete">删除</el-button>
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElCard, ElTag, ElRow, ElCol, ElButton } from 'element-plus'
import { Requirement } from '@/types'

interface Props {
  requirement: Requirement
  showActions?: boolean
}

interface Emits {
  (e: 'view', requirement: Requirement): void
  (e: 'edit', requirement: Requirement): void
  (e: 'publish', id: string): void
  (e: 'delete', id: string): void
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true
})

const emit = defineEmits<Emits>()
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'DRAFT': 'info',
    'PUBLISHED': 'success',
    'CLOSED': 'warning',
    'CANCELLED': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'DRAFT': '草稿',
    'PUBLISHED': '已发布',
    'CLOSED': '已关闭',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

const formatBudget = (budget: any) => {
  if (!budget) return '面议'
  return `${budget.minAmount} - ${budget.maxAmount}`
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const handleView = () => {
  emit('view', props.requirement)
}

const handleEdit = () => {
  emit('edit', props.requirement)
}

const handlePublish = () => {
  emit('publish', props.requirement.id)
}

const handleDelete = () => {
  emit('delete', props.requirement.id)
}
</script>

<style scoped>
.requirement-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-weight: 600;
  font-size: 16px;
}

.description {
  color: #666;
  margin-bottom: 16px;
  line-height: 1.5;
}

.meta-info {
  margin-bottom: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.label {
  color: #999;
  margin-right: 8px;
  min-width: 60px;
}

.value {
  color: #333;
  font-weight: 500;
}

.card-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}
</style>
```

### 3.2 供应商档案组件
```vue
<!-- 供应商档案组件 - components/SupplierProfile.vue -->
<template>
  <el-card class="supplier-profile" shadow="hover">
    <div class="supplier-header">
      <el-avatar :size="64" :src="supplier.logo">
        <el-icon><User /></el-icon>
      </el-avatar>

      <div class="supplier-info">
        <h3 class="company-name">{{ supplier.companyName }}</h3>

        <div class="supplier-ratings">
          <el-rate
            v-model="supplier.overallScore"
            disabled
            show-score
            text-color="#ff9900"
          />
          <span class="rating-text">
            ({{ supplier.totalOrders }}笔订单)
          </span>
        </div>

        <div class="supplier-tags">
          <el-tag
            v-for="category in supplier.categories"
            :key="category"
            size="small"
            style="margin-right: 8px;"
          >
            {{ category }}
          </el-tag>
        </div>
      </div>
    </div>

    <div v-if="showContactInfo" class="supplier-contact">
      <el-descriptions :column="2" size="small">
        <el-descriptions-item label="成立年份">
          {{ supplier.establishedYear }}
        </el-descriptions-item>
        <el-descriptions-item label="员工数量">
          {{ supplier.employeeCount }}
        </el-descriptions-item>
        <el-descriptions-item label="注册资本">
          {{ supplier.registeredCapital }}
        </el-descriptions-item>
        <el-descriptions-item label="主营业务">
          {{ supplier.mainBusiness }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer v-if="showActions">
      <div class="supplier-actions">
        <el-button size="small" @click="handleContact">联系供应商</el-button>
        <el-button size="small" type="primary" @click="handleViewProducts">查看产品</el-button>
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import { ElCard, ElAvatar, ElIcon, ElRate, ElTag, ElDescriptions, ElDescriptionsItem, ElButton } from 'element-plus'
import { User } from '@element-plus/icons-vue'
import { SupplierProfile } from '@/types'

interface Props {
  supplier: SupplierProfile
  showContactInfo?: boolean
  showActions?: boolean
}

interface Emits {
  (e: 'contact', supplier: SupplierProfile): void
  (e: 'viewProducts', supplierId: string): void
}

const props = withDefaults(defineProps<Props>(), {
  showContactInfo: false,
  showActions: true
})

const emit = defineEmits<Emits>()

const handleContact = () => {
  emit('contact', props.supplier)
}

const handleViewProducts = () => {
  emit('viewProducts', props.supplier.id)
}
      
      <div class="supplier-profile__content">
        <Descriptions column={2} size="small">
          <Descriptions.Item label="成立年份">
            {supplier.establishedYear}
          </Descriptions.Item>
          <Descriptions.Item label="员工数量">
            {supplier.employeeCount}人
          </Descriptions.Item>
          <Descriptions.Item label="服务区域">
            {supplier.serviceRegions.join(', ')}
          </Descriptions.Item>
          <Descriptions.Item label="认证资质">
            {supplier.certifications.length}项
          </Descriptions.Item>
        </Descriptions>
        
        {showContactInfo && (
          <div class="contact-info">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="联系电话">
                {supplier.contactPhone}
              </Descriptions.Item>
              <Descriptions.Item label="联系邮箱">
                {supplier.contactEmail}
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </div>
      
      {showActions && (
        <div class="supplier-profile__actions">
          <el-button
            type="primary"
            icon={<MessageOutlined />}
            onClick={() => onContact?.(supplier)}
          >
            联系供应商
          </Button>
          <el-button
            icon={<ShopOutlined />}
            onClick={() => onViewProducts?.(supplier.id)}
          >
            查看产品
          </Button>
        </div>
      )}
    </Card>
  );
};
```

### 3.2 智能补货组件
```typescript
// 智能补货组件
interface IntelligentReplenishmentProps {
  inventoryId: string;
  onSuccess?: () => void;
}

export const IntelligentReplenishment: Vue Component<IntelligentReplenishmentProps> = ({
  inventoryId,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const { data: recommendation, isLoading } = useGetReplenishmentRecommendationQuery(inventoryId);
  const [executeReplenishment, { isLoading: isExecuting }] = useExecuteOneClickReplenishmentMutation();
  
  const handleExecute = async (values: OneClickReplenishmentForm) => {
    try {
      await executeReplenishment({
        inventoryId,
        supplierId: recommendation!.supplier.id,
        quantity: values.quantity,
        maxUnitPrice: values.maxUnitPrice,
        deliveryAddress: values.deliveryAddress,
        urgency: values.urgency
      }).unwrap();
      
      ElMessage.success('智能补货订单已生成，正在通知供应商确认');
      onSuccess?.();
    } catch (error) {
      ElMessage.error('补货失败，请重试或联系客服');
    }
  };
  
  if (isLoading) {
    return (
      <el-card>
        <Skeleton active />
      </Card>
    );
  }
  
  if (!recommendation || recommendation.type !== 'ONE_CLICK') {
    return (
      <el-card title="智能补货">
        <Alert
          message="无法执行智能补货"
          description={recommendation?.reason || '未找到合适的信任供应商，建议使用传统采购流程'}
          type="warning"
          showIcon
          action={
            <el-button size="small" type="primary">
              传统采购
            </Button>
          }
        />
      </Card>
    );
  }
  
  return (
    <el-card title="智能补货建议" class="intelligent-replenishment">
      {/* 推荐供应商信息 */}
      <div class="recommended-supplier">
        <Title level={5}>推荐供应商</Title>
        <div class="supplier-card">
          <Avatar src={recommendation.supplier.logo} size={48} />
          <div class="supplier-info">
            <div class="supplier-name">{recommendation.supplier.name}</div>
            <div class="supplier-metrics">
              <Tag color="green">
                信任度: {(recommendation.supplier.trustScore * 100).toFixed(1)}%
              </Tag>
              <Tag color="blue">
                历史订单: {recommendation.supplier.totalOrders}笔
              </Tag>
              <Tag color="orange">
                平均评分: {recommendation.supplier.averageRating.toFixed(1)}
              </Tag>
            </div>
          </div>
        </div>
      </div>
      
      {/* 历史价格趋势 */}
      <div class="price-history">
        <Title level={5}>历史价格趋势</Title>
        <div style={{ height: 200 }}>
          <Line
            data={{
              labels: recommendation.priceHistory.map(p => formatDate(p.date)),
              datasets: [{
                label: '单价 (¥)',
                data: recommendation.priceHistory.map(p => p.unitPrice),
                borderColor: '#1890ff',
                backgroundColor: 'rgba(24, 144, 255, 0.1)',
                tension: 0.4,
                fill: true
              }]
            }}
            options={{
              responsive: true,
              maintainAspectRatio: false,
              scales: {
                y: {
                  beginAtZero: false,
                  ticks: {
                    callback: (value) => `¥${value}`
                  }
                }
              },
              plugins: {
                legend: {
                  display: false
                },
                tooltip: {
                  callbacks: {
                    label: (context) => `单价: ¥${context.parsed.y}`
                  }
                }
              }
            }}
          />
        </div>
      </div>
      
      {/* 补货表单 */}
      <el-form
        form={form}
        layout="vertical"
        onFinish={handleExecute}
        initialValues={{
          quantity: recommendation.recommendedQuantity,
          maxUnitPrice: recommendation.estimatedUnitPrice,
          urgency: 'NORMAL'
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <el-form.Item
              name="quantity"
              label="补货数量"
              rules={[
                { required: true, message: '请输入补货数量' },
                { type: 'number', min: 1, message: '数量必须大于0' }
              ]}
            >
              <InputNumber
                min={1}
                style={{ width: '100%' }}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value!.replace(/\$\s?|(,*)/g, '')}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <el-form.Item
              name="maxUnitPrice"
              label="最高单价"
              rules={[
                { required: true, message: '请输入最高单价' },
                { type: 'number', min: 0, message: '价格不能为负数' }
              ]}
            >
              <InputNumber
                min={0}
                precision={2}
                style={{ width: '100%' }}
                formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value!.replace(/¥\s?|(,*)/g, '')}
              />
            </Form.Item>
          </Col>
        </Row>
        
        <el-form.Item
          name="deliveryAddress"
          label="收货地址"
          rules={[{ required: true, message: '请选择收货地址' }]}
        >
          <Select placeholder="请选择收货地址">
            {/* 地址选项从用户配置中获取 */}
          </Select>
        </Form.Item>
        
        <el-form.Item
          name="urgency"
          label="紧急程度"
        >
          <Radio.Group>
            <Radio value="LOW">普通</Radio>
            <Radio value="NORMAL">一般</Radio>
            <Radio value="HIGH">紧急</Radio>
            <Radio value="URGENT">特急</Radio>
          </Radio.Group>
        </Form.Item>
        
        <el-form.Item>
          <el-button
            type="primary"
            htmlType="submit"
            size="large"
            block
            loading={isExecuting}
            icon={<ThunderboltOutlined />}
          >
            一键智能补货 (预计{recommendation.estimatedDeliveryDays}天到货)
          </Button>
        </Form.Item>
      </Form>
      
      {/* 可信度指示器 */}
      <div class="confidence-indicator">
        <div class="confidence-label">推荐可信度</div>
        <Progress
          percent={recommendation.confidenceLevel * 100}
          strokeColor={{
            '0%': '#ff4d4f',
            '50%': '#faad14',
            '100%': '#52c41a'
          }}
          format={percent => `${percent}%`}
        />
        <div class="confidence-tips">
          <Text type="secondary">
            基于历史交易数据、供应商信任度和市场价格分析
          </Text>
        </div>
      </div>
    </Card>
  );
};
```

## 4. 性能优化策略

### 4.1 代码分割和懒加载
```typescript
// 路由级别的代码分割
const RequirementPages = lazy(() => 
  import('./pages/requirements').then(module => ({
    default: module.RequirementPages
  }))
);

// 组件级别的懒加载
const HeavyChart = lazy(() => import('./components/charts/HeavyChart'));

// 使用Suspense包装
<Suspense fallback={<ChartSkeleton />}>
  <HeavyChart data={chartData} />
</Suspense>

// 预加载关键路由
const preloadRequirementPages = () => import('./pages/requirements');

// 在用户hover时预加载
<Link
  to="/requirements"
  onMouseEnter={preloadRequirementPages}
>
  需求管理
</Link>
```

### 4.2 缓存优化策略
```typescript
// Axios + Composables缓存配置
export const api = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api',
  }),
  tagTypes: ['Requirement', 'Supplier', 'Order'],
  // 全局缓存配置
  keepUnusedDataFor: 60, // 60秒
  refetchOnMountOrArgChange: 30, // 30秒内不重新获取
  refetchOnFocus: true,
  refetchOnReconnect: true,
});

// 组件级别的缓存优化
<script setup lang="ts">
interface Props {
  data: any[];
  onUpdate: (id: string, updates: any) => void;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  update: [id: string, updates: any];
}>();

// 使用computed缓存计算结果
const processedData = computed(() => {
  return props.data.map(item => ({
    ...item,
    computed: expensiveCalculation(item)
  }));
});

// 事件处理器
const handleUpdate = (id: string, updates: any) => {
  emit('update', id, updates);
};
  
  return (
    <div>
      {processedData.map(item => (
        <ItemComponent
          key={item.id}
          item={item}
          onUpdate={handleUpdate}
        />
      ))}
    </div>
  );
});
```

这套前端架构设计为超级个人全栈开发者提供了现代化、高性能的前端解决方案，通过Vue 3 + TypeScript + Element Plus的组合，实现了企业级的用户界面和用户体验。
