# 超级个人全栈开发 - 安全实施手册文档

## 1. 安全实施总览

### 1.1 安全设计理念
- **纵深防御**：多层安全防护，确保系统安全性
- **最小权限原则**：用户和服务只获得必要的最小权限
- **数据保护优先**：敏感数据加密存储和传输
- **安全左移**：在开发阶段就考虑安全问题
- **合规性**：符合GDPR、等保等安全合规要求
- **可审计性**：完整的安全审计日志和追踪

### 1.2 安全架构图
```mermaid
graph TB
    subgraph "客户端安全 (Client Security)"
        HTTPS[HTTPS加密传输<br/>TLS 1.3]
        CSP[内容安全策略<br/>CSP Headers]
        XSS[XSS防护<br/>输入验证]
        CSRF[CSRF防护<br/>Token验证]
    end
    
    subgraph "API网关安全 (API Gateway Security)"
        RATE_LIMIT[限流防护<br/>Rate Limiting]
        IP_FILTER[IP白名单<br/>IP Filtering]
        WAF[Web应用防火墙<br/>WAF Protection]
        API_KEY[API密钥管理<br/>API Key Management]
    end
    
    subgraph "认证授权 (Authentication & Authorization)"
        JWT_AUTH[JWT认证<br/>Token Based Auth]
        OAUTH2[OAuth2授权<br/>Authorization Code Flow]
        RBAC[角色权限控制<br/>Role-Based Access Control]
        MFA[多因子认证<br/>Multi-Factor Authentication]
    end
    
    subgraph "数据安全 (Data Security)"
        ENCRYPT_REST[静态数据加密<br/>AES-256]
        ENCRYPT_TRANSIT[传输加密<br/>TLS/SSL]
        DATA_MASK[数据脱敏<br/>Sensitive Data Masking]
        BACKUP_ENCRYPT[备份加密<br/>Encrypted Backups]
    end
    
    subgraph "应用安全 (Application Security)"
        INPUT_VALID[输入验证<br/>Input Validation]
        SQL_INJECT[SQL注入防护<br/>Prepared Statements]
        SECURE_CODE[安全编码<br/>Secure Coding]
        DEPENDENCY[依赖安全扫描<br/>Dependency Scanning]
    end
    
    subgraph "监控审计 (Monitoring & Auditing)"
        SECURITY_LOG[安全日志<br/>Security Logging]
        INTRUSION[入侵检测<br/>Intrusion Detection]
        ALERT[安全告警<br/>Security Alerts]
        AUDIT_TRAIL[审计追踪<br/>Audit Trail]
    end
    
    HTTPS --> JWT_AUTH
    CSP --> RATE_LIMIT
    JWT_AUTH --> RBAC
    OAUTH2 --> MFA
    
    RBAC --> ENCRYPT_REST
    INPUT_VALID --> SQL_INJECT
    ENCRYPT_REST --> SECURITY_LOG
    SECURITY_LOG --> INTRUSION
```

## 2. JWT认证实施

### 2.1 JWT配置和实现
```kotlin
// JWT配置类
@Configuration
@EnableWebSecurity
class JwtSecurityConfig {
    
    @Value("\${app.jwt.secret}")
    private lateinit var jwtSecret: String
    
    @Value("\${app.jwt.expiration}")
    private var jwtExpiration: Long = 86400 // 24小时
    
    @Value("\${app.jwt.refresh-expiration}")
    private var refreshExpiration: Long = 604800 // 7天
    
    @Bean
    fun jwtEncoder(): JwtEncoder {
        val jwk = RSAKey.Builder(rsaPublicKey())
            .privateKey(rsaPrivateKey())
            .build()
        val jwks = ImmutableJWKSet(JWKSet(jwk))
        return NimbusJwtEncoder(jwks)
    }
    
    @Bean
    fun jwtDecoder(): JwtDecoder {
        return NimbusJwtDecoder.withPublicKey(rsaPublicKey()).build()
    }
    
    @Bean
    fun passwordEncoder(): PasswordEncoder {
        return BCryptPasswordEncoder(12) // 强度12
    }
    
    private fun rsaPublicKey(): RSAPublicKey {
        // 从配置或密钥库加载RSA公钥
        return loadRSAPublicKey()
    }
    
    private fun rsaPrivateKey(): RSAPrivateKey {
        // 从配置或密钥库加载RSA私钥
        return loadRSAPrivateKey()
    }
}

// JWT工具类
@Component
class JwtTokenProvider {
    
    @Autowired
    private lateinit var jwtEncoder: JwtEncoder
    
    @Autowired
    private lateinit var jwtDecoder: JwtDecoder
    
    @Value("\${app.jwt.expiration}")
    private var jwtExpiration: Long = 86400
    
    fun generateToken(userDetails: UserDetails): String {
        val now = Instant.now()
        val expiry = now.plusSeconds(jwtExpiration)
        
        val claims = JwtClaimsSet.builder()
            .issuer("procurement-platform")
            .subject(userDetails.username)
            .audience(listOf("procurement-api"))
            .issuedAt(now)
            .expiresAt(expiry)
            .claim("authorities", userDetails.authorities.map { it.authority })
            .claim("userId", getUserId(userDetails))
            .claim("tokenType", "ACCESS_TOKEN")
            .build()
        
        return jwtEncoder.encode(JwtEncoderParameters.from(claims)).tokenValue
    }
    
    fun generateRefreshToken(userDetails: UserDetails): String {
        val now = Instant.now()
        val expiry = now.plusSeconds(refreshExpiration)
        
        val claims = JwtClaimsSet.builder()
            .issuer("procurement-platform")
            .subject(userDetails.username)
            .issuedAt(now)
            .expiresAt(expiry)
            .claim("tokenType", "REFRESH_TOKEN")
            .build()
        
        return jwtEncoder.encode(JwtEncoderParameters.from(claims)).tokenValue
    }
    
    fun validateToken(token: String): Boolean {
        return try {
            val jwt = jwtDecoder.decode(token)
            jwt.expiresAt?.isAfter(Instant.now()) ?: false
        } catch (e: Exception) {
            false
        }
    }
    
    fun getUsernameFromToken(token: String): String? {
        return try {
            jwtDecoder.decode(token).subject
        } catch (e: Exception) {
            null
        }
    }
    
    fun getAuthoritiesFromToken(token: String): List<String> {
        return try {
            val jwt = jwtDecoder.decode(token)
            jwt.getClaimAsStringList("authorities") ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }
}
```

### 2.2 JWT认证过滤器
```kotlin
// JWT认证过滤器
@Component
class JwtAuthenticationFilter(
    private val jwtTokenProvider: JwtTokenProvider,
    private val userDetailsService: UserDetailsService
) : OncePerRequestFilter() {
    
    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain
    ) {
        try {
            val token = extractTokenFromRequest(request)
            
            if (token != null && jwtTokenProvider.validateToken(token)) {
                val username = jwtTokenProvider.getUsernameFromToken(token)
                
                if (username != null && SecurityContextHolder.getContext().authentication == null) {
                    val userDetails = userDetailsService.loadUserByUsername(username)
                    
                    if (jwtTokenProvider.validateToken(token)) {
                        val authentication = UsernamePasswordAuthenticationToken(
                            userDetails, null, userDetails.authorities
                        )
                        authentication.details = WebAuthenticationDetailsSource().buildDetails(request)
                        SecurityContextHolder.getContext().authentication = authentication
                        
                        // 记录认证成功日志
                        MDC.put("userId", username)
                        log.debug("JWT认证成功: user={}", username)
                    }
                }
            }
        } catch (e: Exception) {
            log.error("JWT认证失败: {}", e.message)
            // 清除安全上下文
            SecurityContextHolder.clearContext()
        }
        
        filterChain.doFilter(request, response)
    }
    
    private fun extractTokenFromRequest(request: HttpServletRequest): String? {
        val bearerToken = request.getHeader("Authorization")
        return if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            bearerToken.substring(7)
        } else null
    }
}
```

### 2.3 认证控制器
```kotlin
// 认证控制器
@RestController
@RequestMapping("/api/auth")
@Validated
class AuthController {
    
    @Autowired
    private lateinit var authenticationManager: AuthenticationManager
    
    @Autowired
    private lateinit var jwtTokenProvider: JwtTokenProvider
    
    @Autowired
    private lateinit var userService: UserService
    
    @PostMapping("/login")
    fun login(@Valid @RequestBody request: LoginRequest): ResponseEntity<AuthResponse> {
        try {
            // 记录登录尝试
            log.info("用户登录尝试: username={}, ip={}", 
                request.username, getClientIpAddress())
            
            // 认证用户
            val authentication = authenticationManager.authenticate(
                UsernamePasswordAuthenticationToken(request.username, request.password)
            )
            
            val userDetails = authentication.principal as UserDetails
            
            // 生成JWT令牌
            val accessToken = jwtTokenProvider.generateToken(userDetails)
            val refreshToken = jwtTokenProvider.generateRefreshToken(userDetails)
            
            // 更新用户最后登录时间
            userService.updateLastLoginTime(request.username)
            
            // 记录登录成功日志
            auditLogger.info("用户登录成功: username={}, ip={}", 
                request.username, getClientIpAddress())
            
            return ResponseEntity.ok(AuthResponse(
                accessToken = accessToken,
                refreshToken = refreshToken,
                tokenType = "Bearer",
                expiresIn = jwtExpiration
            ))
            
        } catch (e: BadCredentialsException) {
            // 记录登录失败日志
            auditLogger.warn("用户登录失败: username={}, reason={}, ip={}", 
                request.username, "invalid_credentials", getClientIpAddress())
            
            throw AuthenticationException("用户名或密码错误")
        }
    }
    
    @PostMapping("/refresh")
    fun refreshToken(@Valid @RequestBody request: RefreshTokenRequest): ResponseEntity<AuthResponse> {
        try {
            if (!jwtTokenProvider.validateToken(request.refreshToken)) {
                throw AuthenticationException("刷新令牌无效或已过期")
            }
            
            val username = jwtTokenProvider.getUsernameFromToken(request.refreshToken)
                ?: throw AuthenticationException("无法从刷新令牌中获取用户信息")
            
            val userDetails = userDetailsService.loadUserByUsername(username)
            val newAccessToken = jwtTokenProvider.generateToken(userDetails)
            
            log.info("令牌刷新成功: username={}", username)
            
            return ResponseEntity.ok(AuthResponse(
                accessToken = newAccessToken,
                refreshToken = request.refreshToken, // 刷新令牌保持不变
                tokenType = "Bearer",
                expiresIn = jwtExpiration
            ))
            
        } catch (e: Exception) {
            log.warn("令牌刷新失败: {}", e.message)
            throw AuthenticationException("令牌刷新失败")
        }
    }
    
    @PostMapping("/logout")
    @PreAuthorize("isAuthenticated()")
    fun logout(request: HttpServletRequest): ResponseEntity<ApiResponse<String>> {
        val username = SecurityContextHolder.getContext().authentication.name
        
        // 记录登出日志
        auditLogger.info("用户登出: username={}, ip={}", 
            username, getClientIpAddress())
        
        // 清除安全上下文
        SecurityContextHolder.clearContext()
        
        // 这里可以实现令牌黑名单机制
        // tokenBlacklistService.addToBlacklist(token)
        
        return ResponseEntity.ok(ApiResponse.success("登出成功"))
    }
}
```

## 3. OAuth2集成方案

### 3.1 OAuth2配置
```kotlin
// OAuth2配置
@Configuration
@EnableWebSecurity
class OAuth2SecurityConfig {
    
    @Bean
    fun clientRegistrationRepository(): ClientRegistrationRepository {
        return InMemoryClientRegistrationRepository(
            googleClientRegistration(),
            wechatClientRegistration()
        )
    }
    
    private fun googleClientRegistration(): ClientRegistration {
        return ClientRegistration.withRegistrationId("google")
            .clientId("your-google-client-id")
            .clientSecret("your-google-client-secret")
            .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
            .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
            .redirectUri("{baseUrl}/login/oauth2/code/{registrationId}")
            .scope("openid", "profile", "email")
            .authorizationUri("https://accounts.google.com/o/oauth2/v2/auth")
            .tokenUri("https://www.googleapis.com/oauth2/v4/token")
            .userInfoUri("https://www.googleapis.com/oauth2/v3/userinfo")
            .userNameAttributeName(IdTokenClaimNames.SUB)
            .jwkSetUri("https://www.googleapis.com/oauth2/v3/certs")
            .clientName("Google")
            .build()
    }
    
    private fun wechatClientRegistration(): ClientRegistration {
        return ClientRegistration.withRegistrationId("wechat")
            .clientId("your-wechat-app-id")
            .clientSecret("your-wechat-app-secret")
            .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_POST)
            .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
            .redirectUri("{baseUrl}/login/oauth2/code/{registrationId}")
            .scope("snsapi_userinfo")
            .authorizationUri("https://open.weixin.qq.com/connect/oauth2/authorize")
            .tokenUri("https://api.weixin.qq.com/sns/oauth2/access_token")
            .userInfoUri("https://api.weixin.qq.com/sns/userinfo")
            .userNameAttributeName("openid")
            .clientName("WeChat")
            .build()
    }
}

// OAuth2成功处理器
@Component
class OAuth2AuthenticationSuccessHandler : AuthenticationSuccessHandler {
    
    @Autowired
    private lateinit var jwtTokenProvider: JwtTokenProvider
    
    @Autowired
    private lateinit var userService: UserService
    
    override fun onAuthenticationSuccess(
        request: HttpServletRequest,
        response: HttpServletResponse,
        authentication: Authentication
    ) {
        val oauth2User = authentication.principal as OAuth2User
        val registrationId = getRegistrationId(request)
        
        try {
            // 处理OAuth2用户信息
            val user = processOAuth2User(oauth2User, registrationId)
            
            // 生成JWT令牌
            val userDetails = CustomUserDetails(user)
            val accessToken = jwtTokenProvider.generateToken(userDetails)
            val refreshToken = jwtTokenProvider.generateRefreshToken(userDetails)
            
            // 记录OAuth2登录成功
            auditLogger.info("OAuth2登录成功: provider={}, userId={}, ip={}", 
                registrationId, user.id, getClientIpAddress(request))
            
            // 重定向到前端，携带令牌
            val redirectUrl = buildRedirectUrl(accessToken, refreshToken)
            response.sendRedirect(redirectUrl)
            
        } catch (e: Exception) {
            log.error("OAuth2认证处理失败: {}", e.message, e)
            response.sendRedirect("/login?error=oauth2_failed")
        }
    }
    
    private fun processOAuth2User(oauth2User: OAuth2User, registrationId: String): User {
        return when (registrationId) {
            "google" -> processGoogleUser(oauth2User)
            "wechat" -> processWeChatUser(oauth2User)
            else -> throw IllegalArgumentException("不支持的OAuth2提供商: $registrationId")
        }
    }
    
    private fun processGoogleUser(oauth2User: OAuth2User): User {
        val email = oauth2User.getAttribute<String>("email")
        val name = oauth2User.getAttribute<String>("name")
        val picture = oauth2User.getAttribute<String>("picture")
        
        return userService.findOrCreateOAuth2User(
            provider = "google",
            providerId = oauth2User.name,
            email = email,
            name = name,
            avatar = picture
        )
    }
}
```

## 4. 数据加密策略

### 4.1 静态数据加密
```kotlin
// 数据库字段加密
@Component
class DatabaseEncryption {

    @Value("\${app.encryption.database.key}")
    private lateinit var encryptionKey: String

    private val cipher = Cipher.getInstance("AES/GCM/NoPadding")
    private val secureRandom = SecureRandom()

    fun encrypt(plainText: String): String {
        val secretKey = SecretKeySpec(encryptionKey.toByteArray(), "AES")
        val iv = ByteArray(12)
        secureRandom.nextBytes(iv)

        cipher.init(Cipher.ENCRYPT_MODE, secretKey, GCMParameterSpec(128, iv))
        val encryptedData = cipher.doFinal(plainText.toByteArray())

        // 将IV和加密数据组合
        val combined = iv + encryptedData
        return Base64.getEncoder().encodeToString(combined)
    }

    fun decrypt(encryptedText: String): String {
        val combined = Base64.getDecoder().decode(encryptedText)
        val iv = combined.sliceArray(0..11)
        val encryptedData = combined.sliceArray(12 until combined.size)

        val secretKey = SecretKeySpec(encryptionKey.toByteArray(), "AES")
        cipher.init(Cipher.DECRYPT_MODE, secretKey, GCMParameterSpec(128, iv))

        val decryptedData = cipher.doFinal(encryptedData)
        return String(decryptedData)
    }
}

// JPA加密转换器
@Converter
class EncryptedStringConverter : AttributeConverter<String, String> {

    @Autowired
    private lateinit var databaseEncryption: DatabaseEncryption

    override fun convertToDatabaseColumn(attribute: String?): String? {
        return attribute?.let { databaseEncryption.encrypt(it) }
    }

    override fun convertToEntityAttribute(dbData: String?): String? {
        return dbData?.let { databaseEncryption.decrypt(it) }
    }
}

// 敏感字段实体示例
@Entity
@Table(name = "users")
class User {
    @Id
    val id: UUID = UUID.randomUUID()

    val username: String = ""

    // 加密存储的敏感字段
    @Convert(converter = EncryptedStringConverter::class)
    @Column(name = "phone_number")
    val phoneNumber: String = ""

    @Convert(converter = EncryptedStringConverter::class)
    @Column(name = "id_card_number")
    val idCardNumber: String? = null

    @Convert(converter = EncryptedStringConverter::class)
    @Column(name = "bank_account")
    val bankAccount: String? = null
}
```

### 4.2 传输加密配置
```yaml
# application.yml - HTTPS配置
server:
  port: 8443
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: ${SSL_KEYSTORE_PASSWORD}
    key-store-type: PKCS12
    key-alias: procurement-platform
    protocol: TLS
    enabled-protocols: TLSv1.3,TLSv1.2
    ciphers:
      - TLS_AES_256_GCM_SHA384
      - TLS_AES_128_GCM_SHA256
      - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256

# HTTP重定向到HTTPS
management:
  server:
    port: 8080
    ssl:
      enabled: false
```

```kotlin
// HTTPS重定向配置
@Configuration
class HttpsRedirectConfig {

    @Bean
    fun httpToHttpsRedirectConnector(): TomcatServletWebServerFactory {
        val tomcat = TomcatServletWebServerFactory()
        tomcat.addAdditionalTomcatConnectors(createHttpConnector())
        return tomcat
    }

    private fun createHttpConnector(): Connector {
        val connector = Connector(TomcatServletWebServerFactory.DEFAULT_PROTOCOL)
        connector.scheme = "http"
        connector.port = 8080
        connector.secure = false
        connector.redirectPort = 8443
        return connector
    }
}
```

## 5. API安全防护

### 5.1 限流和防护
```kotlin
// API限流配置
@Configuration
@EnableWebSecurity
class ApiSecurityConfig {

    @Bean
    fun rateLimitFilter(): RateLimitFilter {
        return RateLimitFilter()
    }

    @Bean
    fun securityFilterChain(http: HttpSecurity): SecurityFilterChain {
        return http
            .csrf { it.disable() }
            .sessionManagement { it.sessionCreationPolicy(SessionCreationPolicy.STATELESS) }
            .authorizeHttpRequests { auth ->
                auth
                    .requestMatchers("/api/auth/**").permitAll()
                    .requestMatchers("/api/public/**").permitAll()
                    .requestMatchers(HttpMethod.GET, "/api/requirements").hasRole("USER")
                    .requestMatchers(HttpMethod.POST, "/api/requirements").hasRole("BUYER")
                    .requestMatchers("/api/admin/**").hasRole("ADMIN")
                    .anyRequest().authenticated()
            }
            .addFilterBefore(rateLimitFilter(), UsernamePasswordAuthenticationFilter::class.java)
            .addFilterBefore(jwtAuthenticationFilter(), UsernamePasswordAuthenticationFilter::class.java)
            .exceptionHandling {
                it.authenticationEntryPoint(customAuthenticationEntryPoint())
                it.accessDeniedHandler(customAccessDeniedHandler())
            }
            .headers { headers ->
                headers
                    .frameOptions().deny()
                    .contentTypeOptions().and()
                    .httpStrictTransportSecurity { hsts ->
                        hsts.maxAgeInSeconds(31536000)
                        hsts.includeSubdomains(true)
                        hsts.preload(true)
                    }
                    .and()
                    .addHeaderWriter { request, response ->
                        response.setHeader("X-Content-Type-Options", "nosniff")
                        response.setHeader("X-Frame-Options", "DENY")
                        response.setHeader("X-XSS-Protection", "1; mode=block")
                        response.setHeader("Referrer-Policy", "strict-origin-when-cross-origin")
                        response.setHeader("Content-Security-Policy",
                            "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'")
                    }
            }
            .build()
    }
}

// 限流过滤器
@Component
class RateLimitFilter : OncePerRequestFilter() {

    private val rateLimiter = ConcurrentHashMap<String, RateLimiter>()

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain
    ) {
        val clientId = getClientIdentifier(request)
        val limiter = rateLimiter.computeIfAbsent(clientId) {
            RateLimiter.create(100.0) // 每秒100个请求
        }

        if (limiter.tryAcquire()) {
            filterChain.doFilter(request, response)
        } else {
            // 记录限流日志
            log.warn("API限流触发: clientId={}, uri={}", clientId, request.requestURI)

            response.status = HttpStatus.TOO_MANY_REQUESTS.value()
            response.contentType = "application/json"
            response.writer.write("""
                {
                    "success": false,
                    "errorCode": "RATE_LIMIT_EXCEEDED",
                    "message": "请求过于频繁，请稍后重试",
                    "timestamp": "${Instant.now()}"
                }
            """.trimIndent())
        }
    }

    private fun getClientIdentifier(request: HttpServletRequest): String {
        // 优先使用用户ID，其次使用IP地址
        val authentication = SecurityContextHolder.getContext().authentication
        return if (authentication != null && authentication.isAuthenticated) {
            "user:${authentication.name}"
        } else {
            "ip:${getClientIpAddress(request)}"
        }
    }
}
```

### 5.2 输入验证和SQL注入防护
```kotlin
// 输入验证注解
@Target(AnnotationTarget.FIELD, AnnotationTarget.VALUE_PARAMETER)
@Retention(AnnotationRetention.RUNTIME)
@Constraint(validatedBy = [SafeStringValidator::class])
annotation class SafeString(
    val message: String = "输入包含不安全字符",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = []
)

// 安全字符串验证器
class SafeStringValidator : ConstraintValidator<SafeString, String> {

    private val dangerousPatterns = listOf(
        "<script[^>]*>.*?</script>".toRegex(RegexOption.IGNORE_CASE),
        "javascript:".toRegex(RegexOption.IGNORE_CASE),
        "on\\w+\\s*=".toRegex(RegexOption.IGNORE_CASE),
        "\\b(union|select|insert|update|delete|drop|create|alter)\\b".toRegex(RegexOption.IGNORE_CASE)
    )

    override fun isValid(value: String?, context: ConstraintValidatorContext): Boolean {
        if (value == null) return true

        return dangerousPatterns.none { pattern ->
            pattern.containsMatchIn(value)
        }
    }
}

// 请求DTO示例
data class CreateRequirementRequest(
    @field:NotBlank(message = "标题不能为空")
    @field:Size(max = 200, message = "标题长度不能超过200字符")
    @field:SafeString
    val title: String,

    @field:Size(max = 2000, message = "描述长度不能超过2000字符")
    @field:SafeString
    val description: String?,

    @field:NotNull(message = "预算不能为空")
    @field:DecimalMin(value = "0.01", message = "预算必须大于0")
    val budget: BigDecimal
)

// Repository安全实现
@Repository
interface RequirementRepository : JpaRepository<ProcurementRequirement, UUID> {

    // 使用参数化查询防止SQL注入
    @Query("""
        SELECT r FROM ProcurementRequirement r
        WHERE r.buyerId = :buyerId
        AND r.status = :status
        AND (:keyword IS NULL OR r.title LIKE %:keyword% OR r.description LIKE %:keyword%)
        ORDER BY r.createdAt DESC
    """)
    fun findByBuyerIdAndStatusWithKeyword(
        @Param("buyerId") buyerId: UUID,
        @Param("status") status: RequirementStatus,
        @Param("keyword") keyword: String?
    ): List<ProcurementRequirement>

    // 使用Specification进行动态查询
    fun findAll(spec: Specification<ProcurementRequirement>): List<ProcurementRequirement>
}
```

## 6. 敏感数据处理

### 6.1 数据脱敏
```kotlin
// 数据脱敏工具
@Component
class DataMaskingService {

    fun maskPhoneNumber(phone: String?): String? {
        return phone?.let {
            if (it.length >= 11) {
                "${it.substring(0, 3)}****${it.substring(7)}"
            } else it
        }
    }

    fun maskEmail(email: String?): String? {
        return email?.let {
            val atIndex = it.indexOf('@')
            if (atIndex > 3) {
                "${it.substring(0, 3)}***${it.substring(atIndex)}"
            } else it
        }
    }

    fun maskIdCard(idCard: String?): String? {
        return idCard?.let {
            if (it.length >= 18) {
                "${it.substring(0, 6)}********${it.substring(14)}"
            } else it
        }
    }

    fun maskBankAccount(account: String?): String? {
        return account?.let {
            if (it.length >= 16) {
                "${it.substring(0, 4)}****${it.substring(it.length - 4)}"
            } else it
        }
    }
}

// 响应DTO脱敏
data class UserProfileResponse(
    val id: UUID,
    val username: String,
    val email: String,
    val phoneNumber: String?,
    val realName: String?,
    val idCardNumber: String?,
    val bankAccount: String?
) {
    companion object {
        fun from(user: User, maskingService: DataMaskingService): UserProfileResponse {
            return UserProfileResponse(
                id = user.id,
                username = user.username,
                email = maskingService.maskEmail(user.email) ?: "",
                phoneNumber = maskingService.maskPhoneNumber(user.phoneNumber),
                realName = user.realName,
                idCardNumber = maskingService.maskIdCard(user.idCardNumber),
                bankAccount = maskingService.maskBankAccount(user.bankAccount)
            )
        }
    }
}
```

### 6.2 敏感操作审计
```kotlin
// 审计注解
@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class Auditable(
    val operation: String,
    val resourceType: String = "",
    val sensitiveData: Boolean = false
)

// 审计切面
@Aspect
@Component
class AuditAspect {

    private val auditLogger = LoggerFactory.getLogger("AUDIT")

    @Around("@annotation(auditable)")
    fun auditMethod(joinPoint: ProceedingJoinPoint, auditable: Auditable): Any? {
        val startTime = System.currentTimeMillis()
        val authentication = SecurityContextHolder.getContext().authentication
        val userId = authentication?.name ?: "anonymous"
        val userRoles = authentication?.authorities?.map { it.authority } ?: emptyList()

        // 记录操作开始
        val auditLog = AuditLog(
            userId = userId,
            operation = auditable.operation,
            resourceType = auditable.resourceType,
            timestamp = Instant.now(),
            ipAddress = getCurrentRequestIpAddress(),
            userAgent = getCurrentRequestUserAgent(),
            parameters = if (auditable.sensitiveData) "[MASKED]" else extractParameters(joinPoint)
        )

        try {
            val result = joinPoint.proceed()

            // 记录操作成功
            auditLog.status = "SUCCESS"
            auditLog.duration = System.currentTimeMillis() - startTime

            auditLogger.info("审计日志: {}", auditLog.toJson())

            return result

        } catch (e: Exception) {
            // 记录操作失败
            auditLog.status = "FAILED"
            auditLog.errorMessage = e.message
            auditLog.duration = System.currentTimeMillis() - startTime

            auditLogger.error("审计日志: {}", auditLog.toJson())

            throw e
        }
    }

    private fun extractParameters(joinPoint: ProceedingJoinPoint): String {
        return try {
            val args = joinPoint.args
            objectMapper.writeValueAsString(args)
        } catch (e: Exception) {
            "[EXTRACTION_FAILED]"
        }
    }
}

// 使用审计注解
@Service
class UserService {

    @Auditable(operation = "UPDATE_SENSITIVE_INFO", resourceType = "USER", sensitiveData = true)
    fun updateSensitiveInfo(userId: UUID, request: UpdateSensitiveInfoRequest) {
        // 敏感信息更新逻辑
    }

    @Auditable(operation = "VIEW_USER_PROFILE", resourceType = "USER")
    fun getUserProfile(userId: UUID): UserProfileResponse {
        // 用户资料查看逻辑
    }
}
```

## 7. 前端安全实施

### 7.1 前端安全配置
```typescript
// 前端安全工具
class SecurityUtils {
    // XSS防护 - HTML转义
    static escapeHtml(unsafe: string): string {
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;")
    }

    // 输入验证
    static validateInput(input: string, type: 'email' | 'phone' | 'general'): boolean {
        const patterns = {
            email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            phone: /^1[3-9]\d{9}$/,
            general: /^[a-zA-Z0-9\u4e00-\u9fa5\s\-_.,!?()]+$/
        }

        return patterns[type].test(input)
    }

    // 敏感数据脱敏
    static maskSensitiveData(data: string, type: 'phone' | 'email' | 'idCard'): string {
        switch (type) {
            case 'phone':
                return data.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
            case 'email':
                return data.replace(/([^@]{1,3})[^@]*(@.*)/, '$1***$2')
            case 'idCard':
                return data.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
            default:
                return data
        }
    }

    // CSRF Token管理
    static getCsrfToken(): string {
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        return token || ''
    }

    // 安全的本地存储
    static secureStorage = {
        set(key: string, value: any, encrypt: boolean = false): void {
            const data = encrypt ? this.encrypt(JSON.stringify(value)) : JSON.stringify(value)
            localStorage.setItem(key, data)
        },

        get(key: string, decrypt: boolean = false): any {
            const data = localStorage.getItem(key)
            if (!data) return null

            try {
                const parsed = decrypt ? this.decrypt(data) : data
                return JSON.parse(parsed)
            } catch {
                return null
            }
        },

        remove(key: string): void {
            localStorage.removeItem(key)
        },

        encrypt(data: string): string {
            // 简单的Base64编码，生产环境应使用更强的加密
            return btoa(data)
        },

        decrypt(data: string): string {
            return atob(data)
        }
    }
}
```

### 7.2 HTTP安全配置
```typescript
// axios安全配置
import axios from 'axios'

// 创建安全的axios实例
const secureAxios = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL,
    timeout: 30000,
    withCredentials: true, // 发送cookies
    headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    }
})

// 请求拦截器 - 添加安全头
secureAxios.interceptors.request.use(
    (config) => {
        // 添加CSRF Token
        const csrfToken = SecurityUtils.getCsrfToken()
        if (csrfToken) {
            config.headers['X-CSRF-TOKEN'] = csrfToken
        }

        // 添加JWT Token
        const token = SecurityUtils.secureStorage.get('access_token')
        if (token) {
            config.headers.Authorization = `Bearer ${token}`
        }

        // 添加请求ID用于追踪
        config.headers['X-Request-ID'] = generateRequestId()

        return config
    },
    (error) => {
        return Promise.reject(error)
    }
)

// 响应拦截器 - 安全检查
secureAxios.interceptors.response.use(
    (response) => {
        // 检查响应头的安全性
        const securityHeaders = [
            'X-Content-Type-Options',
            'X-Frame-Options',
            'X-XSS-Protection'
        ]

        securityHeaders.forEach(header => {
            if (!response.headers[header.toLowerCase()]) {
                console.warn(`缺少安全头: ${header}`)
            }
        })

        return response
    },
    (error) => {
        // 安全错误处理
        if (error.response?.status === 401) {
            // 清除本地认证信息
            SecurityUtils.secureStorage.remove('access_token')
            SecurityUtils.secureStorage.remove('refresh_token')

            // 跳转到登录页
            window.location.href = '/login'
        }

        return Promise.reject(error)
    }
)

function generateRequestId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

export default secureAxios
```

## 8. 安全监控和告警

### 8.1 安全事件监控
```kotlin
// 安全事件监控
@Component
class SecurityEventMonitor {

    private val securityLogger = LoggerFactory.getLogger("SECURITY")
    private val alertService = AlertService()

    @EventListener
    fun handleAuthenticationFailure(event: AuthenticationFailureBadCredentialsEvent) {
        val username = event.authentication.name
        val ipAddress = getClientIpAddress()

        securityLogger.warn("认证失败: username={}, ip={}, timestamp={}",
            username, ipAddress, Instant.now())

        // 检查是否为暴力破解攻击
        if (isbruteForceAttack(username, ipAddress)) {
            alertService.sendSecurityAlert(
                "暴力破解攻击检测",
                "检测到针对用户 $username 的暴力破解攻击，来源IP: $ipAddress"
            )

            // 临时锁定账户
            userService.temporaryLockAccount(username, Duration.ofMinutes(15))
        }
    }

    @EventListener
    fun handleAccessDenied(event: AuthorizationFailureEvent) {
        val username = event.authentication.name
        val resource = event.source.toString()

        securityLogger.warn("访问被拒绝: username={}, resource={}, timestamp={}",
            username, resource, Instant.now())

        // 检查是否为权限提升攻击
        if (isPrivilegeEscalationAttempt(username, resource)) {
            alertService.sendSecurityAlert(
                "权限提升攻击检测",
                "用户 $username 尝试访问未授权资源: $resource"
            )
        }
    }

    @EventListener
    fun handleSuspiciousActivity(event: SuspiciousActivityEvent) {
        securityLogger.error("可疑活动: type={}, details={}, timestamp={}",
            event.activityType, event.details, Instant.now())

        alertService.sendSecurityAlert(
            "可疑活动检测",
            "检测到可疑活动: ${event.activityType}, 详情: ${event.details}"
        )
    }

    private fun isbruteForceAttack(username: String, ipAddress: String): Boolean {
        // 检查最近5分钟内的失败次数
        val failureCount = getRecentFailureCount(username, ipAddress, Duration.ofMinutes(5))
        return failureCount >= 5
    }

    private fun isPrivilegeEscalationAttempt(username: String, resource: String): Boolean {
        // 检查用户是否频繁尝试访问高权限资源
        val adminResourcePattern = "/api/admin/.*".toRegex()
        return adminResourcePattern.matches(resource) && !userService.hasAdminRole(username)
    }
}
```

### 8.2 安全审计报告
```kotlin
// 安全审计报告生成
@Service
class SecurityAuditService {

    fun generateSecurityReport(startDate: LocalDate, endDate: LocalDate): SecurityReport {
        val auditLogs = auditLogRepository.findByDateRange(startDate, endDate)

        return SecurityReport(
            period = DateRange(startDate, endDate),
            totalOperations = auditLogs.size,
            failedOperations = auditLogs.count { it.status == "FAILED" },
            sensitiveOperations = auditLogs.count { it.operation.contains("SENSITIVE") },
            topUsers = getTopActiveUsers(auditLogs),
            securityEvents = getSecurityEvents(auditLogs),
            recommendations = generateSecurityRecommendations(auditLogs)
        )
    }

    private fun getSecurityEvents(auditLogs: List<AuditLog>): List<SecurityEvent> {
        return auditLogs
            .filter { it.operation.contains("LOGIN_FAILED") || it.operation.contains("ACCESS_DENIED") }
            .groupBy { it.ipAddress }
            .map { (ip, logs) ->
                SecurityEvent(
                    type = "SUSPICIOUS_IP",
                    description = "IP地址 $ip 有 ${logs.size} 次安全事件",
                    severity = when {
                        logs.size > 10 -> "HIGH"
                        logs.size > 5 -> "MEDIUM"
                        else -> "LOW"
                    },
                    count = logs.size
                )
            }
    }

    private fun generateSecurityRecommendations(auditLogs: List<AuditLog>): List<String> {
        val recommendations = mutableListOf<String>()

        val failureRate = auditLogs.count { it.status == "FAILED" }.toDouble() / auditLogs.size
        if (failureRate > 0.1) {
            recommendations.add("失败率较高(${String.format("%.2f", failureRate * 100)}%)，建议加强输入验证")
        }

        val sensitiveOpsCount = auditLogs.count { it.operation.contains("SENSITIVE") }
        if (sensitiveOpsCount > 100) {
            recommendations.add("敏感操作频繁($sensitiveOpsCount 次)，建议加强监控和审批流程")
        }

        return recommendations
    }
}
```

## 9. 安全最佳实践

### 9.1 开发安全检查清单
```markdown
## 安全开发检查清单

### 认证和授权
- [ ] 实施强密码策略
- [ ] 使用多因子认证
- [ ] JWT令牌安全配置
- [ ] 会话管理安全
- [ ] 权限最小化原则

### 数据保护
- [ ] 敏感数据加密存储
- [ ] 传输层加密(HTTPS)
- [ ] 数据脱敏处理
- [ ] 备份数据加密
- [ ] 密钥安全管理

### 输入验证
- [ ] 所有输入进行验证
- [ ] SQL注入防护
- [ ] XSS攻击防护
- [ ] CSRF攻击防护
- [ ] 文件上传安全

### API安全
- [ ] API限流配置
- [ ] 安全头设置
- [ ] 错误信息安全
- [ ] API版本管理
- [ ] 接口访问控制

### 监控和审计
- [ ] 安全日志记录
- [ ] 异常行为监控
- [ ] 审计追踪完整
- [ ] 安全告警机制
- [ ] 定期安全评估
```

### 9.2 安全配置模板
```yaml
# 生产环境安全配置模板
app:
  security:
    # JWT配置
    jwt:
      secret: ${JWT_SECRET} # 从环境变量获取
      expiration: 3600 # 1小时
      refresh-expiration: 86400 # 24小时

    # 密码策略
    password:
      min-length: 8
      require-uppercase: true
      require-lowercase: true
      require-numbers: true
      require-special-chars: true
      max-attempts: 5
      lockout-duration: 900 # 15分钟

    # 会话配置
    session:
      timeout: 1800 # 30分钟
      max-concurrent: 1

    # 限流配置
    rate-limit:
      requests-per-second: 100
      burst-capacity: 200

    # 加密配置
    encryption:
      algorithm: "AES/GCM/NoPadding"
      key-size: 256

    # 审计配置
    audit:
      enabled: true
      sensitive-operations: true
      retention-days: 365
```

这个安全实施手册提供了完整的安全解决方案，涵盖了从认证授权到数据保护的各个方面，确保您的采购平台具备企业级的安全防护能力。
```
```
```
