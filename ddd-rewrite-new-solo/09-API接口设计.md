# 超级个人全栈开发 - API接口设计文档

## 1. API设计总览

### 1.1 RESTful API设计原则
- **资源导向**：以业务资源为中心设计API
- **HTTP语义**：正确使用HTTP方法和状态码
- **统一响应**：标准化的响应格式和错误处理
- **版本管理**：支持API版本演进和向后兼容
- **安全优先**：JWT认证、权限控制、数据验证
- **文档驱动**：OpenAPI 3.0规范，自动生成文档

### 1.2 API架构图
```mermaid
graph TB
    subgraph "客户端层 (Client Layer)"
        WEB[Web前端<br/>Vue 3 + TypeScript]
        MOBILE[移动端<br/>Vue 3 + Vant]
        API_CLIENT[第三方客户端<br/>OpenAPI SDK]
    end
    
    subgraph "API网关层 (API Gateway)"
        GATEWAY[Spring Boot Gateway<br/>统一入口]
        AUTH[JWT认证<br/>权限验证]
        RATE[限流熔断<br/>Resilience4j]
        LOG[请求日志<br/>审计追踪]
    end
    
    subgraph "控制器层 (Controller Layer)"
        REQ_CTRL[需求管理API<br/>/api/requirements]
        SUP_CTRL[供应商API<br/>/api/suppliers]
        BID_CTRL[竞价API<br/>/api/bids]
        ORD_CTRL[订单API<br/>/api/orders]
        INV_CTRL[库存API<br/>/api/inventory]
        USR_CTRL[用户洞察API<br/>/api/user-insights]
        GRW_CTRL[用户增长API<br/>/api/user-growth]
        ANA_CTRL[数据分析API<br/>/api/analytics]
        REC_CTRL[智能推荐API<br/>/api/recommendations]
        DEC_CTRL[智能决策API<br/>/api/decisions]
        COMP_CTRL[合规管理API<br/>/api/compliance]
        RISK_CTRL[风险控制API<br/>/api/risk-control]
    end

    subgraph "应用服务层 (Application Layer)"
        REQ_SVC[需求管理服务]
        SUP_SVC[供应商服务]
        BID_SVC[竞价服务]
        ORD_SVC[订单服务]
        INV_SVC[库存服务]
        USR_SVC[用户洞察服务]
        GRW_SVC[用户增长服务]
        ANA_SVC[数据分析服务]
        REC_SVC[智能推荐服务]
        DEC_SVC[智能决策服务]
        COMP_SVC[合规管理服务]
        RISK_SVC[风险控制服务]
    end

    subgraph "领域层 (Domain Layer)"
        DOMAIN[17个限界上下文<br/>领域模型]
    end
    
    WEB --> GATEWAY
    MOBILE --> GATEWAY
    API_CLIENT --> GATEWAY
    
    GATEWAY --> AUTH
    GATEWAY --> RATE
    GATEWAY --> LOG
    
    AUTH --> REQ_CTRL
    AUTH --> SUP_CTRL
    AUTH --> BID_CTRL
    AUTH --> ORD_CTRL
    AUTH --> INV_CTRL
    AUTH --> ANA_CTRL
    
    REQ_CTRL --> REQ_SVC
    SUP_CTRL --> SUP_SVC
    BID_CTRL --> BID_SVC
    ORD_CTRL --> ORD_SVC
    INV_CTRL --> INV_SVC
    ANA_CTRL --> ANA_SVC
    
    REQ_SVC --> DOMAIN
    SUP_SVC --> DOMAIN
    BID_SVC --> DOMAIN
    ORD_SVC --> DOMAIN
    INV_SVC --> DOMAIN
    ANA_SVC --> DOMAIN
```

## 2. 统一API规范

### 2.1 统一响应格式
```kotlin
// 统一响应包装类
@JsonInclude(JsonInclude.Include.NON_NULL)
data class ApiResponse<T>(
    val success: Boolean,
    val data: T? = null,
    val message: String? = null,
    val errors: List<String>? = null,
    val timestamp: LocalDateTime = LocalDateTime.now(),
    val requestId: String = UUID.randomUUID().toString()
) {
    companion object {
        fun <T> success(data: T, message: String? = null): ApiResponse<T> {
            return ApiResponse(success = true, data = data, message = message)
        }
        
        fun <T> error(message: String, errors: List<String>? = null): ApiResponse<T> {
            return ApiResponse(success = false, message = message, errors = errors)
        }
        
        fun <T> error(exception: Exception): ApiResponse<T> {
            return ApiResponse(
                success = false,
                message = exception.message ?: "未知错误",
                errors = listOfNotNull(exception.cause?.message)
            )
        }
    }
}

// 分页响应格式
data class PagedResponse<T>(
    val content: List<T>,
    val page: Int,
    val size: Int,
    val totalElements: Long,
    val totalPages: Int,
    val first: Boolean,
    val last: Boolean,
    val hasNext: Boolean,
    val hasPrevious: Boolean
) {
    companion object {
        fun <T> of(page: Page<T>): PagedResponse<T> {
            return PagedResponse(
                content = page.content,
                page = page.number,
                size = page.size,
                totalElements = page.totalElements,
                totalPages = page.totalPages,
                first = page.isFirst,
                last = page.isLast,
                hasNext = page.hasNext(),
                hasPrevious = page.hasPrevious()
            )
        }
    }
}

// 全局异常处理
@RestControllerAdvice
class GlobalExceptionHandler {
    
    @ExceptionHandler(ValidationException::class)
    fun handleValidationException(ex: ValidationException): ResponseEntity<ApiResponse<Nothing>> {
        return ResponseEntity.badRequest().body(
            ApiResponse.error<Nothing>(
                message = "参数验证失败",
                errors = ex.errors
            )
        )
    }
    
    @ExceptionHandler(EntityNotFoundException::class)
    fun handleEntityNotFoundException(ex: EntityNotFoundException): ResponseEntity<ApiResponse<Nothing>> {
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(
            ApiResponse.error<Nothing>(
                message = "资源不存在",
                errors = listOf(ex.message ?: "未找到指定资源")
            )
        )
    }
    
    @ExceptionHandler(AccessDeniedException::class)
    fun handleAccessDeniedException(ex: AccessDeniedException): ResponseEntity<ApiResponse<Nothing>> {
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(
            ApiResponse.error<Nothing>(
                message = "访问被拒绝",
                errors = listOf("您没有权限执行此操作")
            )
        )
    }
    
    @ExceptionHandler(Exception::class)
    fun handleGenericException(ex: Exception): ResponseEntity<ApiResponse<Nothing>> {
        logger.error("未处理的异常", ex)
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
            ApiResponse.error<Nothing>(
                message = "服务器内部错误",
                errors = if (isDevelopment()) listOf(ex.message ?: "未知错误") else null
            )
        )
    }
}
```

### 2.2 API版本管理
```kotlin
// 版本控制配置
@Configuration
class ApiVersionConfig {
    
    @Bean
    fun apiVersionRequestMappingHandlerMapping(): RequestMappingHandlerMapping {
        return ApiVersionRequestMappingHandlerMapping()
    }
}

// 版本注解
@Target(AnnotationTarget.CLASS, AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class ApiVersion(val value: String)

// 控制器版本示例
@RestController
@RequestMapping("/api/v1/requirements")
@ApiVersion("1.0")
class RequirementV1Controller {
    // V1 API实现
}

@RestController
@RequestMapping("/api/v2/requirements")
@ApiVersion("2.0")
class RequirementV2Controller {
    // V2 API实现，向后兼容
}
```

## 3. 核心业务API设计

### 3.1 需求管理API
```kotlin
@RestController
@RequestMapping("/api/requirements")
@Validated
@Tag(name = "需求管理", description = "采购需求的创建、管理和发布")
class RequirementController(
    private val requirementService: RequirementApplicationService
) {
    
    @GetMapping
    @Operation(summary = "获取需求列表", description = "分页查询采购需求列表")
    fun getRequirements(
        @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") page: Int,
        @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") size: Int,
        @Parameter(description = "需求分类") @RequestParam(required = false) category: String?,
        @Parameter(description = "需求状态") @RequestParam(required = false) status: RequirementStatus?,
        @Parameter(description = "买家ID") @RequestParam(required = false) buyerId: UUID?,
        @Parameter(description = "关键词搜索") @RequestParam(required = false) keyword: String?
    ): ResponseEntity<ApiResponse<PagedResponse<RequirementDto>>> {
        
        val query = RequirementQuery(
            page = page,
            size = size,
            category = category,
            status = status,
            buyerId = buyerId,
            keyword = keyword
        )
        
        val requirements = requirementService.getRequirements(query)
        return ResponseEntity.ok(
            ApiResponse.success(
                data = PagedResponse.of(requirements),
                message = "需求列表获取成功"
            )
        )
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取需求详情", description = "根据ID获取采购需求详细信息")
    fun getRequirement(
        @Parameter(description = "需求ID") @PathVariable id: UUID
    ): ResponseEntity<ApiResponse<RequirementDetailDto>> {
        
        val requirement = requirementService.getRequirementDetail(id)
        return ResponseEntity.ok(
            ApiResponse.success(
                data = requirement,
                message = "需求详情获取成功"
            )
        )
    }
    
    @PostMapping
    @Operation(summary = "创建需求", description = "创建新的采购需求")
    fun createRequirement(
        @Parameter(description = "需求创建请求") @Valid @RequestBody request: CreateRequirementRequest,
        @AuthenticationPrincipal user: UserPrincipal
    ): ResponseEntity<ApiResponse<RequirementDto>> {
        
        val command = CreateRequirementCommand(
            buyerId = user.userId,
            title = request.title,
            description = request.description,
            category = request.category,
            specifications = request.specifications,
            budget = request.budget,
            deadline = request.deadline,
            priority = request.priority
        )
        
        val requirement = requirementService.createRequirement(command)
        return ResponseEntity.status(HttpStatus.CREATED).body(
            ApiResponse.success(
                data = requirement,
                message = "需求创建成功"
            )
        )
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "更新需求", description = "更新采购需求信息")
    @PreAuthorize("@requirementSecurityService.canEdit(#id, authentication.principal.userId)")
    fun updateRequirement(
        @Parameter(description = "需求ID") @PathVariable id: UUID,
        @Parameter(description = "需求更新请求") @Valid @RequestBody request: UpdateRequirementRequest,
        @AuthenticationPrincipal user: UserPrincipal
    ): ResponseEntity<ApiResponse<RequirementDto>> {
        
        val command = UpdateRequirementCommand(
            requirementId = id,
            buyerId = user.userId,
            title = request.title,
            description = request.description,
            specifications = request.specifications,
            budget = request.budget,
            deadline = request.deadline
        )
        
        val requirement = requirementService.updateRequirement(command)
        return ResponseEntity.ok(
            ApiResponse.success(
                data = requirement,
                message = "需求更新成功"
            )
        )
    }
    
    @PostMapping("/{id}/publish")
    @Operation(summary = "发布需求", description = "将草稿状态的需求发布到平台")
    @PreAuthorize("@requirementSecurityService.canPublish(#id, authentication.principal.userId)")
    fun publishRequirement(
        @Parameter(description = "需求ID") @PathVariable id: UUID,
        @AuthenticationPrincipal user: UserPrincipal
    ): ResponseEntity<ApiResponse<RequirementDto>> {
        
        val command = PublishRequirementCommand(
            requirementId = id,
            buyerId = user.userId
        )
        
        val requirement = requirementService.publishRequirement(command)
        return ResponseEntity.ok(
            ApiResponse.success(
                data = requirement,
                message = "需求发布成功"
            )
        )
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "删除需求", description = "删除采购需求")
    @PreAuthorize("@requirementSecurityService.canDelete(#id, authentication.principal.userId)")
    fun deleteRequirement(
        @Parameter(description = "需求ID") @PathVariable id: UUID,
        @AuthenticationPrincipal user: UserPrincipal
    ): ResponseEntity<ApiResponse<Nothing>> {
        
        val command = DeleteRequirementCommand(
            requirementId = id,
            buyerId = user.userId
        )
        
        requirementService.deleteRequirement(command)
        return ResponseEntity.ok(
            ApiResponse.success<Nothing>(
                data = null,
                message = "需求删除成功"
            )
        )
    }
    
    @PostMapping("/{id}/attachments")
    @Operation(summary = "上传附件", description = "为需求上传附件文件")
    fun uploadAttachment(
        @Parameter(description = "需求ID") @PathVariable id: UUID,
        @Parameter(description = "附件文件") @RequestParam("file") file: MultipartFile,
        @AuthenticationPrincipal user: UserPrincipal
    ): ResponseEntity<ApiResponse<AttachmentDto>> {
        
        val command = UploadAttachmentCommand(
            requirementId = id,
            buyerId = user.userId,
            file = file
        )
        
        val attachment = requirementService.uploadAttachment(command)
        return ResponseEntity.ok(
            ApiResponse.success(
                data = attachment,
                message = "附件上传成功"
            )
        )
    }
}

// 需求DTO定义
@Schema(description = "需求信息")
data class RequirementDto(
    @Schema(description = "需求ID") val id: UUID,
    @Schema(description = "买家ID") val buyerId: UUID,
    @Schema(description = "需求标题") val title: String,
    @Schema(description = "需求描述") val description: String,
    @Schema(description = "需求分类") val category: String,
    @Schema(description = "预算信息") val budget: BudgetDto?,
    @Schema(description = "截止日期") val deadline: LocalDate?,
    @Schema(description = "需求状态") val status: RequirementStatus,
    @Schema(description = "优先级") val priority: RequirementPriority,
    @Schema(description = "创建时间") val createdAt: LocalDateTime,
    @Schema(description = "更新时间") val updatedAt: LocalDateTime
)

@Schema(description = "创建需求请求")
data class CreateRequirementRequest(
    @Schema(description = "需求标题", example = "采购办公用品")
    @field:NotBlank(message = "标题不能为空")
    @field:Size(max = 200, message = "标题长度不能超过200字符")
    val title: String,
    
    @Schema(description = "需求描述", example = "需要采购一批办公桌椅")
    @field:NotBlank(message = "描述不能为空")
    @field:Size(max = 2000, message = "描述长度不能超过2000字符")
    val description: String,
    
    @Schema(description = "需求分类", example = "OFFICE_SUPPLIES")
    @field:NotBlank(message = "分类不能为空")
    val category: String,
    
    @Schema(description = "规格要求")
    val specifications: List<SpecificationDto> = emptyList(),
    
    @Schema(description = "预算信息")
    @field:Valid
    val budget: BudgetDto?,
    
    @Schema(description = "截止日期")
    @field:Future(message = "截止日期必须是未来时间")
    val deadline: LocalDate?,
    
    @Schema(description = "优先级", example = "MEDIUM")
    val priority: RequirementPriority = RequirementPriority.MEDIUM
)
```

### 3.2 智能补货API
```kotlin
@RestController
@RequestMapping("/api/replenishment")
@Validated
@Tag(name = "智能补货", description = "基于信任关系的智能补货功能")
class IntelligentReplenishmentController(
    private val replenishmentService: IntelligentReplenishmentApplicationService
) {
    
    @GetMapping("/recommendations/{inventoryId}")
    @Operation(summary = "获取补货建议", description = "基于库存情况和信任供应商生成补货建议")
    fun getReplenishmentRecommendation(
        @Parameter(description = "库存ID") @PathVariable inventoryId: UUID,
        @AuthenticationPrincipal user: UserPrincipal
    ): ResponseEntity<ApiResponse<ReplenishmentRecommendationDto>> {
        
        val query = GetReplenishmentRecommendationQuery(
            inventoryId = inventoryId,
            buyerId = user.userId
        )
        
        val recommendation = replenishmentService.getReplenishmentRecommendation(query)
        return ResponseEntity.ok(
            ApiResponse.success(
                data = recommendation,
                message = "补货建议生成成功"
            )
        )
    }
    
    @PostMapping("/execute")
    @Operation(summary = "执行一键补货", description = "基于推荐的信任供应商执行一键补货")
    fun executeOneClickReplenishment(
        @Parameter(description = "一键补货请求") @Valid @RequestBody request: OneClickReplenishmentRequest,
        @AuthenticationPrincipal user: UserPrincipal
    ): ResponseEntity<ApiResponse<ReplenishmentExecutionDto>> {
        
        val command = ExecuteOneClickReplenishmentCommand(
            inventoryId = request.inventoryId,
            buyerId = user.userId,
            supplierId = request.supplierId,
            quantity = request.quantity,
            maxUnitPrice = request.maxUnitPrice,
            deliveryAddress = request.deliveryAddress,
            urgency = request.urgency,
            notes = request.notes
        )
        
        val execution = replenishmentService.executeOneClickReplenishment(command)
        return ResponseEntity.ok(
            ApiResponse.success(
                data = execution,
                message = "一键补货执行成功，正在通知供应商确认"
            )
        )
    }
    
    @GetMapping("/strategies")
    @Operation(summary = "获取补货策略", description = "获取用户的补货策略配置")
    fun getReplenishmentStrategies(
        @Parameter(description = "页码") @RequestParam(defaultValue = "0") page: Int,
        @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") size: Int,
        @Parameter(description = "产品代码") @RequestParam(required = false) productCode: String?,
        @AuthenticationPrincipal user: UserPrincipal
    ): ResponseEntity<ApiResponse<PagedResponse<ReplenishmentStrategyDto>>> {
        
        val query = GetReplenishmentStrategiesQuery(
            buyerId = user.userId,
            page = page,
            size = size,
            productCode = productCode
        )
        
        val strategies = replenishmentService.getReplenishmentStrategies(query)
        return ResponseEntity.ok(
            ApiResponse.success(
                data = PagedResponse.of(strategies),
                message = "补货策略获取成功"
            )
        )
    }
    
    @PostMapping("/strategies")
    @Operation(summary = "创建补货策略", description = "为特定产品创建智能补货策略")
    fun createReplenishmentStrategy(
        @Parameter(description = "补货策略创建请求") @Valid @RequestBody request: CreateReplenishmentStrategyRequest,
        @AuthenticationPrincipal user: UserPrincipal
    ): ResponseEntity<ApiResponse<ReplenishmentStrategyDto>> {
        
        val command = CreateReplenishmentStrategyCommand(
            buyerId = user.userId,
            productCode = request.productCode,
            productName = request.productName,
            reorderPoint = request.reorderPoint,
            safetyStock = request.safetyStock,
            maxStockLevel = request.maxStockLevel,
            preferredOrderQuantity = request.preferredOrderQuantity,
            maxUnitPrice = request.maxUnitPrice,
            autoReplenishmentEnabled = request.autoReplenishmentEnabled
        )
        
        val strategy = replenishmentService.createReplenishmentStrategy(command)
        return ResponseEntity.status(HttpStatus.CREATED).body(
            ApiResponse.success(
                data = strategy,
                message = "补货策略创建成功"
            )
        )
    }
    
    @GetMapping("/executions")
    @Operation(summary = "获取补货执行记录", description = "查询补货执行历史记录")
    fun getReplenishmentExecutions(
        @Parameter(description = "页码") @RequestParam(defaultValue = "0") page: Int,
        @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") size: Int,
        @Parameter(description = "执行状态") @RequestParam(required = false) status: ReplenishmentExecutionStatus?,
        @Parameter(description = "开始日期") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) startDate: LocalDate?,
        @Parameter(description = "结束日期") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) endDate: LocalDate?,
        @AuthenticationPrincipal user: UserPrincipal
    ): ResponseEntity<ApiResponse<PagedResponse<ReplenishmentExecutionDto>>> {
        
        val query = GetReplenishmentExecutionsQuery(
            buyerId = user.userId,
            page = page,
            size = size,
            status = status,
            startDate = startDate,
            endDate = endDate
        )
        
        val executions = replenishmentService.getReplenishmentExecutions(query)
        return ResponseEntity.ok(
            ApiResponse.success(
                data = PagedResponse.of(executions),
                message = "补货执行记录获取成功"
            )
        )
    }
}

// 补货相关DTO
@Schema(description = "补货建议")
data class ReplenishmentRecommendationDto(
    @Schema(description = "建议类型") val type: ReplenishmentRecommendationType,
    @Schema(description = "推荐供应商") val supplier: TrustedSupplierDto?,
    @Schema(description = "推荐数量") val recommendedQuantity: Int?,
    @Schema(description = "预估单价") val estimatedUnitPrice: BigDecimal?,
    @Schema(description = "历史价格") val priceHistory: List<PriceHistoryDto>,
    @Schema(description = "可信度") val confidenceLevel: BigDecimal,
    @Schema(description = "预计交期") val estimatedDeliveryDays: Int?,
    @Schema(description = "建议原因") val reason: String?
)

@Schema(description = "一键补货请求")
data class OneClickReplenishmentRequest(
    @Schema(description = "库存ID")
    @field:NotNull(message = "库存ID不能为空")
    val inventoryId: UUID,
    
    @Schema(description = "供应商ID")
    @field:NotNull(message = "供应商ID不能为空")
    val supplierId: UUID,
    
    @Schema(description = "补货数量")
    @field:Min(value = 1, message = "补货数量必须大于0")
    val quantity: Int,
    
    @Schema(description = "最高单价")
    @field:DecimalMin(value = "0.01", message = "最高单价必须大于0")
    val maxUnitPrice: BigDecimal,
    
    @Schema(description = "收货地址")
    @field:NotBlank(message = "收货地址不能为空")
    val deliveryAddress: String,
    
    @Schema(description = "紧急程度")
    val urgency: ReplenishmentUrgency = ReplenishmentUrgency.NORMAL,
    
    @Schema(description = "备注")
    val notes: String?
)
```

## 4. OpenAPI文档配置

### 4.1 OpenAPI配置
```kotlin
@Configuration
@OpenAPIDefinition(
    info = Info(
        title = "采购生态平台API",
        description = "企业级B2B采购生态平台的完整API接口文档",
        version = "1.0.0",
        contact = Contact(
            name = "超级个人开发者",
            email = "<EMAIL>"
        ),
        license = License(
            name = "MIT License",
            url = "https://opensource.org/licenses/MIT"
        )
    ),
    servers = [
        Server(url = "https://api.procurement-platform.com", description = "生产环境"),
        Server(url = "https://staging-api.procurement-platform.com", description = "测试环境"),
        Server(url = "http://localhost:8080", description = "开发环境")
    ]
)
@SecurityScheme(
    name = "bearerAuth",
    type = SecuritySchemeType.HTTP,
    bearerFormat = "JWT",
    scheme = "bearer"
)
class OpenApiConfig {
    
    @Bean
    fun customOpenAPI(): OpenAPI {
        return OpenAPI()
            .components(
                Components()
                    .addSecuritySchemes("bearerAuth", 
                        SecurityScheme()
                            .type(SecurityScheme.Type.HTTP)
                            .scheme("bearer")
                            .bearerFormat("JWT")
                    )
            )
            .addSecurityItem(
                SecurityRequirement().addList("bearerAuth")
            )
    }
}
```

### 4.2 前端API客户端生成
```typescript
// 自动生成的API客户端类型
export interface RequirementDto {
  id: string;
  buyerId: string;
  title: string;
  description: string;
  category: string;
  budget?: BudgetDto;
  deadline?: string;
  status: RequirementStatus;
  priority: RequirementPriority;
  createdAt: string;
  updatedAt: string;
}

export interface CreateRequirementRequest {
  title: string;
  description: string;
  category: string;
  specifications?: SpecificationDto[];
  budget?: BudgetDto;
  deadline?: string;
  priority?: RequirementPriority;
}

// 自动生成的API客户端
export class RequirementApi {
  constructor(private apiClient: ApiClient) {}
  
  async getRequirements(params: GetRequirementsParams): Promise<ApiResponse<PagedResponse<RequirementDto>>> {
    return this.apiClient.get('/api/requirements', { params });
  }
  
  async getRequirement(id: string): Promise<ApiResponse<RequirementDetailDto>> {
    return this.apiClient.get(`/api/requirements/${id}`);
  }
  
  async createRequirement(request: CreateRequirementRequest): Promise<ApiResponse<RequirementDto>> {
    return this.apiClient.post('/api/requirements', request);
  }
  
  async updateRequirement(id: string, request: UpdateRequirementRequest): Promise<ApiResponse<RequirementDto>> {
    return this.apiClient.put(`/api/requirements/${id}`, request);
  }
  
  async publishRequirement(id: string): Promise<ApiResponse<RequirementDto>> {
    return this.apiClient.post(`/api/requirements/${id}/publish`);
  }
  
  async deleteRequirement(id: string): Promise<ApiResponse<void>> {
    return this.apiClient.delete(`/api/requirements/${id}`);
  }
}
```

## 8. 新增API接口设计

### 8.1 用户洞察API (/api/user-insights)
```yaml
# 用户洞察API规范
/api/user-insights:
  get:
    summary: 获取用户洞察列表
    parameters:
      - name: userId
        in: query
        schema:
          type: string
          format: uuid
    responses:
      200:
        description: 用户洞察列表
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserInsightListResponse'

/api/user-insights/{id}:
  get:
    summary: 获取用户洞察详情
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      200:
        description: 用户洞察详情
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserInsightResponse'

  put:
    summary: 更新用户洞察
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/UpdateUserInsightRequest'
    responses:
      200:
        description: 更新成功
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserInsightResponse'
```

### 8.2 智能推荐API (/api/recommendations)
```yaml
# 智能推荐API规范
/api/recommendations/suppliers:
  post:
    summary: 获取供应商推荐
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/SupplierRecommendationRequest'
    responses:
      200:
        description: 供应商推荐列表
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SupplierRecommendationResponse'

/api/recommendations/products:
  post:
    summary: 获取产品推荐
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ProductRecommendationRequest'
    responses:
      200:
        description: 产品推荐列表
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductRecommendationResponse'
```

### 8.3 智能决策API (/api/decisions)
```yaml
# 智能决策API规范
/api/decisions/evaluate:
  post:
    summary: 执行智能决策
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DecisionRequest'
    responses:
      200:
        description: 决策结果
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DecisionResponse'

/api/decisions/models:
  get:
    summary: 获取决策模型列表
    responses:
      200:
        description: 决策模型列表
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DecisionModelListResponse'
```

### 8.4 合规管理API (/api/compliance)
```yaml
# 合规管理API规范
/api/compliance/check:
  post:
    summary: 执行合规检查
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ComplianceCheckRequest'
    responses:
      200:
        description: 合规检查结果
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ComplianceCheckResponse'

/api/compliance/policies:
  get:
    summary: 获取合规策略列表
    responses:
      200:
        description: 合规策略列表
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompliancePolicyListResponse'
```

### 8.5 风险控制API (/api/risk-control)
```yaml
# 风险控制API规范
/api/risk-control/assess:
  post:
    summary: 执行风险评估
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/RiskAssessmentRequest'
    responses:
      200:
        description: 风险评估结果
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RiskAssessmentResponse'

/api/risk-control/models:
  get:
    summary: 获取风险模型列表
    responses:
      200:
        description: 风险模型列表
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RiskModelListResponse'
```

## 9. API架构优化总结

这套优化的API接口设计为超级个人全栈开发者提供了完整的RESTful API规范，主要改进包括：

### 9.1 API结构优化
- **用户域重构**：新增用户洞察和用户增长API，替代原有分散的用户相关API
- **智能域扩展**：新增智能推荐和智能决策API，支持AI能力独立演进
- **治理域新增**：新增合规管理和风险控制API，满足企业级治理需求

### 9.2 技术架构优势
- **统一标准**：基于OpenAPI 3.0规范，支持自动化文档生成
- **类型安全**：完整的TypeScript类型定义，前后端类型一致
- **版本管理**：支持API版本演进和向后兼容
- **安全保障**：JWT认证、权限控制、数据验证

### 9.3 开发效率提升
- **代码生成**：基于OpenAPI规范自动生成前后端代码
- **文档同步**：API文档与代码实现自动同步
- **测试支持**：自动生成API测试用例
- **调试便利**：Swagger UI支持在线API调试

### 9.4 长远价值
- **架构演进**：支持从16上下文到17上下文的API演进
- **技术前瞻**：为AI、大数据等新技术预留API接口
- **监管适应**：完善的合规和风险控制API支持

通过OpenAPI 3.0标准化文档和自动化代码生成，实现了高效的前后端协作开发，为长远架构演进提供了稳定的API基础。
