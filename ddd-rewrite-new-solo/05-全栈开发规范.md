# 超级个人全栈开发 - 开发规范文档

## 1. 总体开发原则

### 1.1 DDD开发原则（融入TDD方法）
- **领域优先**：业务逻辑优先于技术实现
- **边界清晰**：严格遵守17个限界上下文边界
- **模型驱动**：以领域模型驱动代码设计
- **事件驱动**：通过领域事件实现上下文解耦
- **测试驱动**：TDD三步法（红-绿-重构）贯穿整个开发过程

### 1.2 全栈开发原则
- **类型安全**：Kotlin + TypeScript全栈类型安全
- **代码一致性**：前后端代码风格和命名保持一致
- **自动化优先**：最大化使用自动化工具和代码生成
- **性能优先**：在保证功能的基础上优化性能
- **可维护性**：编写易于理解和维护的代码
- **TDD优先**：测试驱动开发贯穿前后端开发全过程

### 1.3 TDD开发流程
```mermaid
graph LR
    A[编写失败测试<br/>RED] --> B[编写最少代码<br/>GREEN]
    B --> C[重构优化<br/>REFACTOR]
    C --> A

    subgraph "TDD三步法"
        A
        B
        C
    end
```

#### TDD核心原则
- **红灯**：先写一个失败的测试，明确需求
- **绿灯**：写最少的代码让测试通过
- **重构**：在测试保护下重构和优化代码
- **小步快跑**：每次只实现一个小功能
- **测试即文档**：测试代码就是最好的文档

## 2. 后端Kotlin开发规范

### 2.1 项目结构标准
```
com.procurement.platform
├── shared/                           # 共享组件
│   ├── domain/                       # 共享领域对象
│   │   ├── events/                   # 领域事件定义
│   │   ├── valueobjects/             # 共享值对象
│   │   └── exceptions/               # 领域异常
│   ├── infrastructure/               # 共享基础设施
│   │   ├── config/                   # 配置类
│   │   ├── events/                   # 事件发布器
│   │   └── utils/                    # 工具类
│   └── application/                  # 共享应用服务
├── pretransaction/                   # 交易前置域
│   ├── requirement/                  # 需求管理上下文
│   │   ├── domain/                   # 领域层
│   │   │   ├── model/                # 领域模型
│   │   │   │   ├── aggregates/       # 聚合根
│   │   │   │   ├── entities/         # 实体
│   │   │   │   ├── valueobjects/     # 值对象
│   │   │   │   └── events/           # 领域事件
│   │   │   ├── repository/           # 仓储接口
│   │   │   └── service/              # 领域服务
│   │   ├── application/              # 应用层
│   │   │   ├── service/              # 应用服务
│   │   │   ├── command/              # 命令对象
│   │   │   ├── query/                # 查询对象
│   │   │   └── dto/                  # 数据传输对象
│   │   ├── infrastructure/           # 基础设施层
│   │   │   ├── repository/           # 仓储实现
│   │   │   ├── config/               # 配置
│   │   │   └── external/             # 外部服务
│   │   └── presentation/             # 表现层
│   │       ├── rest/                 # REST控制器
│   │       ├── dto/                  # 表现层DTO
│   │       └── mapper/               # 对象映射器
│   ├── supplier/                     # 供应商发现上下文
│   └── bidding/                      # 竞价评估上下文
├── transaction/                      # 交易执行域
├── posttransaction/                  # 交易后置域
├── userservice/                      # 用户服务域
├── intelligence/                     # 数据智能域
└── platform/                        # 平台服务域
```

### 2.2 Kotlin编码规范
```kotlin
// 1. 命名规范
// 类名：PascalCase
class RequirementApplicationService
data class ProcurementRequirement
interface RequirementRepository

// 函数名和变量名：camelCase
fun createRequirement(command: CreateRequirementCommand): RequirementId
val requirementId: RequirementId
var totalAmount: Money

// 常量：SCREAMING_SNAKE_CASE
const val MAX_REQUIREMENT_ITEMS = 100
const val DEFAULT_CURRENCY = "CNY"

// 包名：小写，用点分隔
package com.procurement.platform.pretransaction.requirement.domain.model

// 2. 类设计规范
// 数据类用于值对象
@Embeddable
data class Money(
    @Column(name = "amount", precision = 15, scale = 2)
    val amount: BigDecimal,
    
    @Column(name = "currency", length = 3)
    val currency: Currency
) {
    init {
        require(amount >= BigDecimal.ZERO) { "金额不能为负数" }
    }
    
    operator fun plus(other: Money): Money {
        require(currency == other.currency) { "货币类型必须相同" }
        return copy(amount = amount + other.amount)
    }
    
    companion object {
        val ZERO = Money(BigDecimal.ZERO, Currency.getInstance("CNY"))
    }
}

// 普通类用于实体和聚合根
@Entity
@Table(schema = "requirement_mgmt", name = "procurement_requirements")
class ProcurementRequirement(
    @EmbeddedId val id: RequirementId,
    val buyerId: UserId,
    var title: String,
    var description: String,
    @Enumerated(EnumType.STRING) var status: RequirementStatus,
    val createdAt: Instant
) {
    // 领域行为方法
    fun publish(): RequirementPublishedEvent {
        require(status == RequirementStatus.DRAFT) { "只能发布草稿状态的需求" }
        status = RequirementStatus.PUBLISHED
        return RequirementPublishedEvent(id.value, buyerId.value)
    }
    
    // 私有方法
    private fun validateTitle() {
        require(title.isNotBlank()) { "标题不能为空" }
        require(title.length <= 200) { "标题长度不能超过200字符" }
    }
}

// 3. 函数设计规范
// 函数应该简短且职责单一
fun createRequirement(command: CreateRequirementCommand): RequirementId {
    // 验证命令
    validateCommand(command)
    
    // 创建聚合根
    val requirement = ProcurementRequirement(
        id = RequirementId.generate(),
        buyerId = UserId(command.buyerId),
        title = command.title,
        description = command.description,
        status = RequirementStatus.DRAFT,
        createdAt = Instant.now()
    )
    
    // 保存并返回
    requirementRepository.save(requirement)
    return requirement.id
}

// 使用扩展函数增强现有类
fun List<OrderItem>.calculateTotal(): Money {
    return this.fold(Money.ZERO) { acc, item -> acc + item.totalPrice }
}

// 使用作用域函数简化代码
fun processOrder(orderId: OrderId): OrderProcessingResult {
    return orderRepository.findById(orderId)?.let { order ->
        order.apply {
            validate()
            updateStatus(OrderStatus.PROCESSING)
        }.let { processedOrder ->
            orderRepository.save(processedOrder)
            OrderProcessingResult.success(processedOrder.id)
        }
    } ?: OrderProcessingResult.notFound(orderId)
}
```

### 2.3 DDD建模规范
```kotlin
// 聚合根设计规范
@Entity
@Table(schema = "order_fulfillment", name = "orders")
class Order(
    @EmbeddedId val id: OrderId,
    val buyerId: UserId,
    val supplierId: UserId,
    @OneToMany(cascade = [CascadeType.ALL], orphanRemoval = true)
    @JoinColumn(name = "order_id")
    private val _items: MutableList<OrderItem> = mutableListOf(),
    @Embedded var totalAmount: Money,
    @Enumerated(EnumType.STRING) var status: OrderStatus,
    val createdAt: Instant
) {
    // 提供不可变视图
    val items: List<OrderItem> get() = _items.toList()
    
    // 领域行为
    fun addItem(item: OrderItem): OrderItemAddedEvent {
        require(status == OrderStatus.DRAFT) { "只能在草稿状态添加商品" }
        require(_items.size < MAX_ORDER_ITEMS) { "订单商品数量不能超过${MAX_ORDER_ITEMS}个" }
        
        _items.add(item)
        recalculateTotal()
        
        return OrderItemAddedEvent(id.value, item.productId, item.quantity)
    }
    
    // 私有方法
    private fun recalculateTotal() {
        totalAmount = _items.calculateTotal()
    }
    
    companion object {
        const val MAX_ORDER_ITEMS = 100
    }
}

// 值对象设计规范
@Embeddable
data class Address(
    @Column(name = "country") val country: String,
    @Column(name = "province") val province: String,
    @Column(name = "city") val city: String,
    @Column(name = "district") val district: String,
    @Column(name = "street") val street: String,
    @Column(name = "postal_code") val postalCode: String
) {
    init {
        require(country.isNotBlank()) { "国家不能为空" }
        require(province.isNotBlank()) { "省份不能为空" }
        require(city.isNotBlank()) { "城市不能为空" }
        require(postalCode.matches(Regex("\\d{6}"))) { "邮政编码格式不正确" }
    }
    
    fun getFullAddress(): String = "$country$province$city$district$street"
    
    fun isInSameCity(other: Address): Boolean {
        return country == other.country && 
               province == other.province && 
               city == other.city
    }
}

// 领域服务设计规范
@Service
class OrderPricingDomainService {
    
    fun calculateOrderTotal(items: List<OrderItem>, discounts: List<Discount>): Money {
        val subtotal = items.calculateTotal()
        val totalDiscount = calculateTotalDiscount(subtotal, discounts)
        return subtotal - totalDiscount
    }
    
    private fun calculateTotalDiscount(subtotal: Money, discounts: List<Discount>): Money {
        return discounts
            .filter { it.isApplicable(subtotal) }
            .fold(Money.ZERO) { acc, discount -> acc + discount.calculateAmount(subtotal) }
    }
}
```

## 3. 前端Vue开发规范

### 3.1 项目结构标准
```
src/
├── components/              # 组件库
│   ├── common/             # 通用组件
│   │   ├── Button/         # 按钮组件
│   │   ├── Form/           # 表单组件
│   │   └── Table/          # 表格组件
│   ├── business/           # 业务组件
│   │   ├── RequirementCard/    # 需求卡片
│   │   ├── SupplierProfile/    # 供应商档案
│   │   └── OrderTimeline/      # 订单时间线
│   └── layout/             # 布局组件
│       ├── Header/         # 页头
│       ├── Sidebar/        # 侧边栏
│       └── Footer/         # 页脚
├── pages/                  # 页面组件
│   ├── requirements/       # 需求管理页面
│   │   ├── RequirementList.vue
│   │   ├── RequirementDetail.vue
│   │   └── CreateRequirement.vue
│   ├── suppliers/          # 供应商管理页面
│   ├── orders/             # 订单管理页面
│   └── analytics/          # 数据分析页面
├── composables/            # 组合式函数
│   ├── useRequirements.ts  # 需求相关组合式函数
│   ├── useSuppliers.ts     # 供应商相关组合式函数
│   └── useAuth.ts          # 认证相关组合式函数
├── services/               # API服务
│   ├── api.ts              # API基础配置
│   ├── requirementApi.ts   # 需求API
│   ├── supplierApi.ts      # 供应商API
│   └── orderApi.ts         # 订单API
├── stores/                 # Pinia状态管理
│   ├── index.ts            # Store配置
│   ├── auth.ts             # 认证状态
│   └── modules/            # 业务模块状态
├── router/                 # Vue Router配置
│   ├── index.ts            # 路由配置
│   └── guards.ts           # 路由守卫
├── types/                  # TypeScript类型
│   ├── requirement.ts      # 需求类型
│   ├── supplier.ts         # 供应商类型
│   └── order.ts            # 订单类型
├── utils/                  # 工具函数
│   ├── format.ts           # 格式化工具
│   ├── validation.ts       # 验证工具
│   └── constants.ts        # 常量定义
└── styles/                 # 样式文件
    ├── globals.css         # 全局样式
    ├── variables.css       # CSS变量
    └── components/         # 组件样式
```

### 3.2 TypeScript编码规范
```typescript
// 1. 接口和类型定义
// 接口名使用PascalCase，以I开头（可选）
interface Requirement {
  id: string;
  title: string;
  description: string;
  category: RequirementCategory;
  budget: Budget;
  status: RequirementStatus;
  createdAt: string;
  updatedAt: string;
}

// 类型别名使用PascalCase
type RequirementId = string;
type UserId = string;

// 枚举使用PascalCase
enum RequirementStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  IN_BIDDING = 'IN_BIDDING',
  CLOSED = 'CLOSED',
  CANCELLED = 'CANCELLED'
}

// 2. Vue组件设计规范
// 使用<script setup>语法
interface RequirementCardProps {
  requirement: Requirement;
  className?: string;
}

interface RequirementCardEmits {
  (e: 'edit', requirement: Requirement): void;
  (e: 'delete', id: string): void;
}

// Vue组件示例
<template>
  <el-card :class="className" class="requirement-card">
    <template #header>
      <span>{{ requirement.title }}</span>
    </template>

    <p>{{ requirement.description }}</p>

    <template #footer>
      <el-button @click="handleEdit">编辑</el-button>
      <el-button type="danger" @click="handleDelete">删除</el-button>
    </template>
  </el-card>
</template>

<script setup lang="ts">
const props = defineProps<RequirementCardProps>()
const emit = defineEmits<RequirementCardEmits>()

const handleEdit = () => {
  emit('edit', props.requirement)
}

const handleDelete = () => {
  emit('delete', props.requirement.id)
}
  
  const handleDelete = () => {
    emit('delete', props.requirement.id);
  };
  
  return (
    <el-card class={className}>
      <el-card.Meta
        title={requirement.title}
        description={requirement.description}
      />
      <div class="requirement-card__actions">
        {onEdit && (
          <el-button type="link" onClick={handleEdit}>
            编辑
          </Button>
        )}
        {onDelete && (
          <el-button type="link" danger onClick={handleDelete}>
            删除
          </Button>
        )}
      </div>
    </Card>
  );
};

// 3. 自定义Hook规范
export const useRequirements = (filters?: RequirementFilters) => {
  const page = ref(1);
  const pageSize = ref(20);
  
  const {
    data,
    isLoading,
    error,
    refetch
  } = useGetRequirementsQuery({
    ...filters,
    page: page - 1,
    size: pageSize
  });
  
  const [createRequirement] = useCreateRequirementMutation();
  const [updateRequirement] = useUpdateRequirementMutation();
  const [deleteRequirement] = useDeleteRequirementMutation();
  
  const handleCreate = async (request: CreateRequirementRequest) => {
    try {
      const result = await requirementStore.createRequirement(request);
      ElMessage.success('需求创建成功');
      return result;
    } catch (error) {
      ElMessage.error('需求创建失败');
      throw error;
    }
  }, [createRequirement]);
  
  return {
    requirements: data?.content || [],
    total: data?.totalElements || 0,
    isLoading,
    error,
    page,
    pageSize,
    setPage,
    setPageSize,
    refetch,
    createRequirement: handleCreate,
    updateRequirement,
    deleteRequirement
  };
};

// 4. 状态管理规范
// Redux Slice设计
const requirementSlice = createSlice({
  name: 'requirements',
  initialState: {
    selectedRequirement: null as Requirement | null,
    filters: {} as RequirementFilters,
    viewMode: 'list' as 'list' | 'grid'
  },
  reducers: {
    setSelectedRequirement: (state, action: PayloadAction<Requirement | null>) => {
      state.selectedRequirement = action.payload;
    },
    setFilters: (state, action: PayloadAction<RequirementFilters>) => {
      state.filters = action.payload;
    },
    setViewMode: (state, action: PayloadAction<'list' | 'grid'>) => {
      state.viewMode = action.payload;
    }
  }
});
```

## 4. API设计规范

### 4.1 RESTful API设计
```kotlin
// 统一响应格式
data class ApiResponse<T>(
    val success: Boolean,
    val data: T? = null,
    val message: String? = null,
    val errors: List<String>? = null,
    val timestamp: LocalDateTime = LocalDateTime.now()
)

// 控制器设计规范
@RestController
@RequestMapping("/api/requirements")
@Validated
class RequirementController(
    private val requirementService: RequirementApplicationService
) {
    
    @GetMapping
    fun getRequirements(
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "20") size: Int,
        @RequestParam(required = false) category: String?,
        @RequestParam(required = false) status: String?
    ): ResponseEntity<ApiResponse<Page<RequirementDto>>> {
        val requirements = requirementService.getRequirements(page, size, category, status)
        return ResponseEntity.ok(ApiResponse(success = true, data = requirements))
    }
    
    @PostMapping
    fun createRequirement(
        @Valid @RequestBody request: CreateRequirementRequest,
        @AuthenticationPrincipal user: UserPrincipal
    ): ResponseEntity<ApiResponse<RequirementDto>> {
        val requirement = requirementService.createRequirement(request, user.userId)
        return ResponseEntity.status(HttpStatus.CREATED)
            .body(ApiResponse(success = true, data = requirement))
    }
}
```

### 4.2 前端API客户端
```typescript
// Axios API服务定义
import { apiService } from '@/services/api'

export const requirementService = {
  async getRequirements(params: RequirementQuery): Promise<PaginatedResponse<Requirement>> {
    return apiService.get('/requirements', { params })
  },

  async createRequirement(data: CreateRequirementRequest): Promise<Requirement> {
    return apiService.post('/requirements', data)
  },

  async updateRequirement(id: string, data: UpdateRequirementRequest): Promise<Requirement> {
    return apiService.put(`/requirements/${id}`, data)
  },

  async deleteRequirement(id: string): Promise<void> {
    return apiService.delete(`/requirements/${id}`)
  }
}

// 组合式函数使用API
export function useRequirements() {
  const requirements = ref<Requirement[]>([])
  const loading = ref(false)

  const fetchRequirements = async (params: RequirementQuery) => {
    loading.value = true
    try {
      const response = await requirementService.getRequirements(params)
      requirements.value = response.content
    } finally {
      loading.value = false
    }
  }

  return {
    requirements,
    loading,
    fetchRequirements
  }
}
```

## 5. 测试规范

### 5.1 后端测试规范
```kotlin
// 单元测试
@ExtendWith(MockKExtension::class)
class RequirementApplicationServiceTest {
    
    @MockK
    private lateinit var requirementRepository: RequirementRepository
    
    @MockK
    private lateinit var eventPublisher: DomainEventPublisher
    
    private lateinit var requirementService: RequirementApplicationService
    
    @BeforeEach
    fun setUp() {
        requirementService = RequirementApplicationService(requirementRepository, eventPublisher)
    }
    
    @Test
    fun `should create requirement successfully`() {
        // Given
        val command = CreateRequirementCommand(
            buyerId = UUID.randomUUID(),
            title = "测试需求",
            description = "测试描述",
            category = RequirementCategory.ELECTRONICS
        )
        
        every { requirementRepository.save(any()) } returns mockk()
        every { eventPublisher.publish(any()) } just Runs
        
        // When
        val result = requirementService.createRequirement(command)
        
        // Then
        assertThat(result).isNotNull
        verify { requirementRepository.save(any()) }
        verify { eventPublisher.publish(any<RequirementCreatedEvent>()) }
    }
}

// 集成测试
@SpringBootTest
@Testcontainers
@Transactional
class RequirementIntegrationTest {
    
    @Container
    companion object {
        @JvmStatic
        val postgres = PostgreSQLContainer<Nothing>("postgres:16")
    }
    
    @Autowired
    private lateinit var requirementService: RequirementApplicationService
    
    @Test
    fun `should create and retrieve requirement`() {
        // Given
        val command = CreateRequirementCommand(
            buyerId = UUID.randomUUID(),
            title = "集成测试需求",
            description = "集成测试描述",
            category = RequirementCategory.ELECTRONICS
        )
        
        // When
        val requirementId = requirementService.createRequirement(command)
        val savedRequirement = requirementService.getRequirement(requirementId)
        
        // Then
        assertThat(savedRequirement).isNotNull
        assertThat(savedRequirement.title).isEqualTo(command.title)
    }
}
```

### 5.2 前端测试规范
```typescript
// Vue组件测试
import { mount } from '@vue/test-utils'
import { ElCard, ElButton } from 'element-plus'
import RequirementCard from '../RequirementCard.vue'

describe('RequirementCard', () => {
  const mockRequirement: Requirement = {
    id: '1',
    title: '测试需求',
    description: '测试描述',
    category: RequirementCategory.ELECTRONICS,
    budget: { minAmount: 1000, maxAmount: 2000, currency: 'CNY' },
    status: RequirementStatus.DRAFT,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }

  it('should render requirement information', () => {
    const wrapper = mount(RequirementCard, {
      props: { requirement: mockRequirement },
      global: {
        components: { ElCard, ElButton }
      }
    })

    expect(wrapper.text()).toContain('测试需求')
    expect(wrapper.text()).toContain('测试描述')
  })

  it('should emit edit event when edit button is clicked', async () => {
    const wrapper = mount(RequirementCard, {
      props: { requirement: mockRequirement },
      global: {
        components: { ElCard, ElButton }
      }
    })

    await wrapper.find('[data-testid="edit-button"]').trigger('click')

    expect(wrapper.emitted('edit')).toBeTruthy()
    expect(wrapper.emitted('edit')?.[0]).toEqual([mockRequirement])
  })
})

// 组合式函数测试
import { useRequirements } from '../useRequirements'

describe('useRequirements', () => {
  it('should fetch requirements on mount', async () => {
    const { result } = renderHook(() => useRequirements());
    
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });
    
    expect(result.current.requirements).toBeDefined();
  });
});
```

## 6. TDD实战指南

### 6.1 后端TDD开发流程

#### 步骤1：编写失败的领域测试（RED）
```kotlin
// 先写测试，定义期望的行为
@Test
fun `should create procurement requirement with valid data`() {
    // Given
    val requirementId = RequirementId.generate()
    val title = "采购笔记本电脑"
    val description = "需要采购100台笔记本电脑"
    val budget = Money(BigDecimal("500000"), Currency.CNY)
    val deadline = LocalDate.now().plusDays(30)

    // When & Then - 这时候代码还不存在，测试会失败
    assertThrows<IllegalArgumentException> {
        ProcurementRequirement.create(
            id = requirementId,
            title = title,
            description = description,
            budget = budget,
            deadline = deadline
        )
    }
}
```

#### 步骤2：编写最少代码让测试通过（GREEN）
```kotlin
// 编写最少的代码让测试通过
class ProcurementRequirement private constructor(
    val id: RequirementId,
    val title: String,
    val description: String,
    val budget: Money,
    val deadline: LocalDate,
    var status: RequirementStatus = RequirementStatus.DRAFT
) {
    companion object {
        fun create(
            id: RequirementId,
            title: String,
            description: String,
            budget: Money,
            deadline: LocalDate
        ): ProcurementRequirement {
            // 基本验证逻辑
            require(title.isNotBlank()) { "标题不能为空" }
            require(description.isNotBlank()) { "描述不能为空" }
            require(budget.amount > BigDecimal.ZERO) { "预算必须大于0" }
            require(deadline.isAfter(LocalDate.now())) { "截止日期必须是未来时间" }

            return ProcurementRequirement(id, title, description, budget, deadline)
        }
    }
}
```

#### 步骤3：重构优化（REFACTOR）
```kotlin
// 在测试保护下重构，提取验证逻辑
class ProcurementRequirement private constructor(
    val id: RequirementId,
    val title: RequirementTitle,
    val description: RequirementDescription,
    val budget: Money,
    val deadline: RequirementDeadline,
    var status: RequirementStatus = RequirementStatus.DRAFT
) {
    companion object {
        fun create(
            id: RequirementId,
            title: String,
            description: String,
            budget: Money,
            deadline: LocalDate
        ): ProcurementRequirement {
            return ProcurementRequirement(
                id = id,
                title = RequirementTitle.of(title),
                description = RequirementDescription.of(description),
                budget = budget,
                deadline = RequirementDeadline.of(deadline)
            )
        }
    }

    fun publish(): RequirementPublishedEvent {
        require(status == RequirementStatus.DRAFT) { "只有草稿状态的需求才能发布" }
        status = RequirementStatus.PUBLISHED
        return RequirementPublishedEvent(id, Instant.now())
    }
}
```

这套TDD融入的全栈开发规范为超级个人开发者提供了完整的测试驱动开发指导，通过红-绿-重构的循环，确保在保持17个限界上下文复杂度的同时，实现高质量的全栈开发。
