# 超级个人全栈开发 - 开发工具链配置文档

## 1. 开发工具链总览

### 1.1 超级个人开发工具链理念
- **效率最大化**：选择能最大化个人开发效率的工具组合
- **TDD优先**：集成完整的TDD工具链，支持测试驱动开发
- **自动化优先**：大量使用自动化工具减少重复劳动
- **质量保证**：集成完整的代码质量检查和测试工具
- **一体化体验**：统一的开发环境和工作流
- **可扩展性**：支持后续团队扩展的工具配置
- **快速反馈**：秒级测试反馈，支持TDD红-绿-重构循环

### 1.2 工具链架构图
```mermaid
graph TB
    subgraph "开发环境 (Development Environment)"
        IDEA[IntelliJ IDEA Ultimate<br/>全栈开发IDE]
        PLUGINS[IDE插件<br/>Kotlin/Vue/Database]
        TEMPLATES[代码模板<br/>Live Templates]
        SETTINGS[IDE配置<br/>统一设置同步]
    end
    
    subgraph "代码管理 (Code Management)"
        GIT[Git<br/>版本控制]
        GITHUB[GitHub<br/>代码托管]
        GITFLOW[Git Flow<br/>分支管理]
        HOOKS[Git Hooks<br/>提交检查]
    end
    
    subgraph "构建工具 (Build Tools)"
        GRADLE[Gradle<br/>后端构建]
        VITE[Vite<br/>前端构建]
        DOCKER[Docker<br/>容器化]
        COMPOSE[Docker Compose<br/>本地环境]
    end
    
    subgraph "代码质量 (Code Quality)"
        KTLINT[Ktlint<br/>Kotlin代码规范]
        ESLINT[ESLint<br/>JavaScript代码规范]
        SONAR[SonarQube<br/>代码质量分析]
        DETEKT[Detekt<br/>Kotlin静态分析]
    end
    
    subgraph "TDD测试工具 (TDD Testing Tools)"
        JUNIT[JUnit 5<br/>单元测试框架]
        MOCKK[MockK<br/>Kotlin Mock框架]
        ASSERTJ[AssertJ<br/>流畅断言库]
        TESTCONTAINERS[Testcontainers<br/>集成测试容器]
        VITEST[Vitest<br/>前端测试框架]
        VTU[Vue Testing Utils<br/>Vue组件测试]
        PLAYWRIGHT[Playwright<br/>E2E测试]
        JACOCO[JaCoCo<br/>代码覆盖率]
    end
    
    subgraph "CI/CD工具 (CI/CD Tools)"
        ACTIONS[GitHub Actions<br/>持续集成]
        WORKFLOWS[工作流<br/>自动化流水线]
        DEPLOY[部署脚本<br/>自动化部署]
        MONITORING[监控<br/>应用监控]
    end
    
    subgraph "文档工具 (Documentation Tools)"
        OPENAPI[OpenAPI<br/>API文档]
        KDOC[KDoc<br/>Kotlin文档]
        JSDOC[JSDoc<br/>JavaScript文档]
        WIKI[项目Wiki<br/>知识管理]
    end
    
    IDEA --> PLUGINS
    IDEA --> TEMPLATES
    IDEA --> SETTINGS
    
    GIT --> GITHUB
    GIT --> GITFLOW
    GIT --> HOOKS
    
    GRADLE --> DOCKER
    VITE --> DOCKER
    DOCKER --> COMPOSE
    
    KTLINT --> SONAR
    ESLINT --> SONAR
    DETEKT --> SONAR
    
    JUNIT --> TESTCONTAINERS
    MOCKK --> TESTCONTAINERS
    JEST --> TESTCONTAINERS
    
    ACTIONS --> WORKFLOWS
    WORKFLOWS --> DEPLOY
    DEPLOY --> MONITORING
    
    OPENAPI --> WIKI
    KDOC --> WIKI
    JSDOC --> WIKI
```

## 2. IntelliJ IDEA 配置

### 2.1 IDE基础配置
```xml
<!-- .idea/codeStyles/Project.xml -->
<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <option name="OTHER_INDENT_OPTIONS">
      <value>
        <option name="INDENT_SIZE" value="4" />
        <option name="TAB_SIZE" value="4" />
        <option name="USE_TAB_CHARACTER" value="false" />
      </value>
    </option>
    <option name="RIGHT_MARGIN" value="120" />
    <option name="WRAP_WHEN_TYPING_REACHES_RIGHT_MARGIN" value="true" />
    
    <!-- Kotlin代码风格 -->
    <codeStyleSettings language="kotlin">
      <option name="RIGHT_MARGIN" value="120" />
      <option name="KEEP_LINE_BREAKS" value="true" />
      <option name="KEEP_FIRST_COLUMN_COMMENT" value="true" />
      <option name="KEEP_CONTROL_STATEMENT_IN_ONE_LINE" value="false" />
      <indentOptions>
        <option name="INDENT_SIZE" value="4" />
        <option name="CONTINUATION_INDENT_SIZE" value="4" />
        <option name="TAB_SIZE" value="4" />
      </indentOptions>
    </codeStyleSettings>
    
    <!-- TypeScript代码风格 -->
    <codeStyleSettings language="TypeScript">
      <option name="RIGHT_MARGIN" value="120" />
      <option name="KEEP_LINE_BREAKS" value="true" />
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
  </code_scheme>
</component>
```

### 2.2 Live Templates配置
```xml
<!-- .idea/templates/Kotlin.xml -->
<templateSet group="Kotlin">
  <!-- DDD聚合根模板 -->
  <template name="aggregate" value="@Entity
@Table(schema = &quot;$SCHEMA$&quot;, name = &quot;$TABLE$&quot;)
class $CLASS_NAME$(
    @EmbeddedId val id: $CLASS_NAME$Id,
    $FIELDS$
) {
    $METHODS$
}" description="DDD聚合根模板" toReformat="true" toShortenFQNames="true">
    <variable name="SCHEMA" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="TABLE" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="CLASS_NAME" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="FIELDS" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="METHODS" expression="" defaultValue="" alwaysStopAt="true" />
    <context>
      <option name="KOTLIN_CLASS" value="true" />
    </context>
  </template>
  
  <!-- 应用服务模板 -->
  <template name="appservice" value="@Service
@Transactional
class $CLASS_NAME$ApplicationService(
    private val $REPOSITORY$: $ENTITY$Repository,
    private val eventPublisher: DomainEventPublisher
) {
    
    fun $METHOD_NAME$(command: $COMMAND$): $RETURN_TYPE$ {
        // 实现应用服务逻辑
        $END$
    }
}" description="应用服务模板" toReformat="true" toShortenFQNames="true">
    <variable name="CLASS_NAME" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="REPOSITORY" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="ENTITY" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="METHOD_NAME" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="COMMAND" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="RETURN_TYPE" expression="" defaultValue="" alwaysStopAt="true" />
    <context>
      <option name="KOTLIN_CLASS" value="true" />
    </context>
  </template>
  
  <!-- 控制器模板 -->
  <template name="controller" value="@RestController
@RequestMapping(&quot;/api/$PATH$&quot;)
@Validated
@Tag(name = &quot;$TAG_NAME$&quot;, description = &quot;$DESCRIPTION$&quot;)
class $CLASS_NAME$Controller(
    private val $SERVICE$: $SERVICE_CLASS$
) {
    
    @GetMapping
    @Operation(summary = &quot;$OPERATION_SUMMARY$&quot;)
    fun get$ENTITIES$(
        @RequestParam(defaultValue = &quot;0&quot;) page: Int,
        @RequestParam(defaultValue = &quot;20&quot;) size: Int
    ): ResponseEntity&lt;ApiResponse&lt;PagedResponse&lt;$DTO$&gt;&gt;&gt; {
        $END$
    }
}" description="REST控制器模板" toReformat="true" toShortenFQNames="true">
    <variable name="PATH" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="TAG_NAME" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="DESCRIPTION" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="CLASS_NAME" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="SERVICE" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="SERVICE_CLASS" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="OPERATION_SUMMARY" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="ENTITIES" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="DTO" expression="" defaultValue="" alwaysStopAt="true" />
    <context>
      <option name="KOTLIN_CLASS" value="true" />
    </context>
  </template>
</templateSet>

<!-- .idea/templates/TypeScript.xml -->
<templateSet group="TypeScript">
  <!-- Vue组件模板 -->
  <template name="vfc" value="&lt;template&gt;
  &lt;div class=&quot;$CSS_CLASS$&quot;&gt;
    $CONTENT$
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup lang=&quot;ts&quot;&gt;
interface Props {
  $PROPS$
}

interface Emits {
  $EMITS$
}

const props = defineProps&lt;Props&gt;()
const emit = defineEmits&lt;Emits&gt;()

$SCRIPT_CONTENT$
&lt;/script&gt;

&lt;style scoped&gt;
.$CSS_CLASS$ {
  $STYLES$
}
&lt;/style&gt;" description="Vue组件模板" toReformat="true" toShortenFQNames="true">
    <variable name="CSS_CLASS" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="CONTENT" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="PROPS" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="EMITS" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="SCRIPT_CONTENT" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="STYLES" expression="" defaultValue="" alwaysStopAt="true" />
    <context>
      <option name="TypeScript" value="true" />
    </context>
  </template>
  
  <!-- Pinia Store模板 -->
  <template name="piniastore" value="import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { $ENTITY$ } from '@/types'
import { $SERVICE_NAME$ } from '@/services'

export const use$STORE_NAME$Store = defineStore('$STORE_ID$', () =&gt; {
  // 状态
  const $ENTITIES$ = ref&lt;$ENTITY$[]&gt;([])
  const loading = ref(false)
  const error = ref&lt;string | null&gt;(null)

  // 计算属性
  const $ENTITIES$Count = computed(() =&gt; $ENTITIES$.value.length)

  // 方法
  async function fetch$ENTITIES$() {
    loading.value = true
    error.value = null
    try {
      const response = await $SERVICE_NAME$.get$ENTITIES$()
      $ENTITIES$.value = response.content
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  async function create$ENTITY$(data: Create$ENTITY$Request) {
    try {
      const new$ENTITY$ = await $SERVICE_NAME$.create$ENTITY$(data)
      $ENTITIES$.value.push(new$ENTITY$)
      return new$ENTITY$
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  return {
    $ENTITIES$,
    loading,
    error,
    $ENTITIES$Count,
    fetch$ENTITIES$,
    create$ENTITY$
  }
})" description="Pinia Store模板" toReformat="true" toShortenFQNames="true">
    <variable name="ENTITY" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="ENTITIES" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="SERVICE_NAME" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="STORE_NAME" expression="" defaultValue="" alwaysStopAt="true" />
    <variable name="STORE_ID" expression="" defaultValue="" alwaysStopAt="true" />
    <context>
      <option name="TypeScript" value="true" />
    </context>
  </template>
</templateSet>
```

### 2.3 必备插件配置
```properties
# .idea/externalDependencies.xml
<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalDependencies">
    <!-- Kotlin开发 -->
    <plugin id="org.jetbrains.kotlin" />
    
    <!-- Spring开发 -->
    <plugin id="com.intellij.spring" />
    <plugin id="com.intellij.spring.boot" />
    
    <!-- 数据库工具 -->
    <plugin id="com.intellij.database" />
    
    <!-- 前端开发 -->
    <plugin id="JavaScript" />
    <plugin id="TypeScript" />
    <plugin id="NodeJS" />
    
    <!-- 代码质量 -->
    <plugin id="org.sonarlint.idea" />
    <plugin id="com.intellij.plugins.html.instantEditing" />
    
    <!-- Git工具 -->
    <plugin id="Git4Idea" />
    <plugin id="com.github.copilot" />
    
    <!-- Docker支持 -->
    <plugin id="Docker" />
    
    <!-- 其他实用工具 -->
    <plugin id="String Manipulation" />
    <plugin id="Rainbow Brackets" />
    <plugin id="Key Promoter X" />
    <plugin id="JPA Buddy" />
  </component>
</project>
```

## 3. 构建工具配置

### 3.1 Gradle配置优化
```kotlin
// build.gradle.kts
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id("org.springframework.boot") version "3.2.0"
    id("io.spring.dependency-management") version "1.1.4"
    id("org.jetbrains.kotlin.jvm") version "1.9.20"
    id("org.jetbrains.kotlin.plugin.spring") version "1.9.20"
    id("org.jetbrains.kotlin.plugin.jpa") version "1.9.20"
    id("org.jlleitschuh.gradle.ktlint") version "11.6.1"
    id("io.gitlab.arturbosch.detekt") version "1.23.4"
    id("org.sonarqube") version "4.4.1.3373"
    id("jacoco")
    id("org.openapi.generator") version "7.1.0"
}

group = "com.procurement.platform"
version = "1.0.0"

java {
    sourceCompatibility = JavaVersion.VERSION_17
}

repositories {
    mavenCentral()
    gradlePluginPortal()
}

dependencies {
    // Spring Boot
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation("org.springframework.boot:spring-boot-starter-cache")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    
    // Kotlin
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin")
    
    // 数据库
    implementation("org.postgresql:postgresql")
    implementation("org.flywaydb:flyway-core")
    implementation("redis.clients:jedis")
    
    // 文档
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.2.0")
    
    // 测试
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework.security:spring-security-test")
    testImplementation("org.testcontainers:junit-jupiter")
    testImplementation("org.testcontainers:postgresql")
    testImplementation("io.mockk:mockk:1.13.8")
    testImplementation("com.ninja-squad:springmockk:4.0.2")
}

tasks.withType<KotlinCompile> {
    kotlinOptions {
        freeCompilerArgs += "-Xjsr305=strict"
        jvmTarget = "17"
    }
}

tasks.withType<Test> {
    useJUnitPlatform()
    finalizedBy(tasks.jacocoTestReport)
}

// Ktlint配置
ktlint {
    version.set("0.50.0")
    debug.set(false)
    verbose.set(true)
    android.set(false)
    outputToConsole.set(true)
    outputColorName.set("RED")
    ignoreFailures.set(false)
    
    filter {
        exclude("**/generated/**")
        include("**/kotlin/**")
    }
}

// Detekt配置
detekt {
    toolVersion = "1.23.4"
    config.setFrom("$projectDir/config/detekt/detekt.yml")
    buildUponDefaultConfig = true
    
    reports {
        html.required.set(true)
        xml.required.set(true)
        txt.required.set(true)
        sarif.required.set(true)
    }
}

// JaCoCo配置
jacoco {
    toolVersion = "0.8.8"
}

tasks.jacocoTestReport {
    dependsOn(tasks.test)
    reports {
        xml.required.set(true)
        html.required.set(true)
        csv.required.set(false)
    }
    
    finalizedBy(tasks.jacocoTestCoverageVerification)
}

tasks.jacocoTestCoverageVerification {
    violationRules {
        rule {
            limit {
                minimum = "0.80".toBigDecimal()
            }
        }
    }
}

// SonarQube配置
sonarqube {
    properties {
        property("sonar.projectKey", "procurement-platform")
        property("sonar.projectName", "Procurement Platform")
        property("sonar.host.url", "http://localhost:9000")
        property("sonar.coverage.jacoco.xmlReportPaths", "build/reports/jacoco/test/jacocoTestReport.xml")
        property("sonar.kotlin.detekt.reportPaths", "build/reports/detekt/detekt.xml")
    }
}

// OpenAPI生成配置
openApiGenerate {
    generatorName.set("typescript-axios")
    inputSpec.set("$projectDir/src/main/resources/openapi/api.yaml")
    outputDir.set("$projectDir/../frontend/src/generated")
    configOptions.set(mapOf(
        "supportsES6" to "true",
        "withInterfaces" to "true",
        "modelPropertyNaming" to "camelCase"
    ))
}
```

### 3.2 前端构建配置
```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';

export default defineConfig({
  plugins: [vue()],
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@pages': resolve(__dirname, 'src/pages'),
      '@composables': resolve(__dirname, 'src/composables'),
      '@services': resolve(__dirname, 'src/services'),
      '@stores': resolve(__dirname, 'src/stores'),
      '@router': resolve(__dirname, 'src/router'),
      '@types': resolve(__dirname, 'src/types'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@styles': resolve(__dirname, 'src/styles'),
    },
  },
  
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
      },
    },
  },
  
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', '@vue/runtime-dom'],
          elementplus: ['element-plus'],
          charts: ['echarts', 'chart.js'],
          utils: ['lodash', 'dayjs'],
        },
      },
    },
  },
  
  optimizeDeps: {
    include: ['vue', '@vue/runtime-dom', 'element-plus'],
  },
});

// package.json scripts
{
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint src --ext ts,vue --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint src --ext ts,vue --fix",
    "type-check": "tsc --noEmit",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:ci": "jest --ci --coverage --watchAll=false"
  }
}
```

## 4. 代码质量配置

### 4.1 ESLint配置
```json
// .eslintrc.json
{
  "env": {
    "browser": true,
    "es2020": true,
    "node": true
  },
  "extends": [
    "eslint:recommended",
    "@typescript-eslint/recommended",
    "plugin:vue/vue3-recommended",
    "@vue/typescript/recommended",
    "prettier"
  ],
  "parser": "vue-eslint-parser",
  "parserOptions": {
    "parser": "@typescript-eslint/parser",
    "ecmaVersion": 2020,
    "sourceType": "module",
    "extraFileExtensions": [".vue"]
  },
  "plugins": [
    "vue",
    "@typescript-eslint",
    "import"
  ],
  "rules": {
    "vue/multi-word-component-names": "off",
    "vue/no-v-html": "warn",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/no-unused-vars": "error",
    "import/order": [
      "error",
      {
        "groups": [
          "builtin",
          "external",
          "internal",
          "parent",
          "sibling",
          "index"
        ],
        "newlines-between": "always"
      }
    ],
    "prefer-const": "error",
    "no-var": "error"
  },
  "settings": {
    "import/resolver": {
      "alias": {
        "map": [
          ["@", "./src"]
        ],
        "extensions": [".ts", ".vue"]
      }
    }
  }
}
```

### 4.2 Prettier配置
```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 120,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "bracketSameLine": false,
  "arrowParens": "always",
  "endOfLine": "lf"
}
```

### 4.3 Detekt配置
```yaml
# config/detekt/detekt.yml
build:
  maxIssues: 0
  excludeCorrectable: false
  weights:
    complexity: 2
    LongParameterList: 1
    style: 1
    comments: 1

config:
  validation: true
  warningsAsErrors: false
  checkExhaustiveness: false

processors:
  active: true
  exclude:
    - 'DetektProgressListener'

console-reports:
  active: true
  exclude:
    - 'ProjectStatisticsReport'
    - 'ComplexityReport'
    - 'NotificationReport'
    - 'FindingsReport'
    - 'FileBasedFindingsReport'

output-reports:
  active: true
  exclude:
    - 'TxtOutputReport'
    - 'XmlOutputReport'
    - 'HtmlOutputReport'

comments:
  active: true
  CommentOverPrivateFunction:
    active: false
  CommentOverPrivateProperty:
    active: false
  EndOfSentenceFormat:
    active: false
    endOfSentenceFormat: '([.?!][ \t\n\r\f<])|([.?!:]$)'
  UndocumentedPublicClass:
    active: false
    searchInNestedClass: true
    searchInInnerClass: true
    searchInInnerObject: true
    searchInInnerInterface: true
  UndocumentedPublicFunction:
    active: false

complexity:
  active: true
  ComplexCondition:
    active: true
    threshold: 4
  ComplexInterface:
    active: false
    threshold: 10
    includeStaticDeclarations: false
    includePrivateDeclarations: false
  ComplexMethod:
    active: true
    threshold: 15
    ignoreSingleWhenExpression: false
    ignoreSimpleWhenEntries: false
    ignoreNestingFunctions: false
    nestingFunctions: [run, let, apply, with, also, use, forEach, isNotNull, ifNull]
  LabeledExpression:
    active: false
    ignoredLabels: []
  LargeClass:
    active: true
    threshold: 600
  LongMethod:
    active: true
    threshold: 60
  LongParameterList:
    active: true
    functionThreshold: 6
    constructorThreshold: 7
    ignoreDefaultParameters: false
    ignoreDataClasses: true
    ignoreAnnotated: []
  MethodOverloading:
    active: false
    threshold: 6
  NestedBlockDepth:
    active: true
    threshold: 4
  StringLiteralDuplication:
    active: false
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    threshold: 3
    ignoreAnnotation: true
    excludeStringsWithLessThan5Characters: true
    ignoreStringsRegex: '$^'
  TooManyFunctions:
    active: true
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    thresholdInFiles: 11
    thresholdInClasses: 11
    thresholdInInterfaces: 11
    thresholdInObjects: 11
    thresholdInEnums: 11
    ignoreDeprecated: false
    ignorePrivate: false
    ignoreOverridden: false

coroutines:
  active: true
  GlobalCoroutineUsage:
    active: false
  RedundantSuspendModifier:
    active: false
  SleepInsteadOfDelay:
    active: true
  SuspendFunWithFlowReturnType:
    active: true

empty-blocks:
  active: true
  EmptyCatchBlock:
    active: true
    allowedExceptionNameRegex: "^(_|(ignore|expected).*)"
  EmptyClassBlock:
    active: true
  EmptyDefaultConstructor:
    active: true
  EmptyDoWhileBlock:
    active: true
  EmptyElseBlock:
    active: true
  EmptyFinallyBlock:
    active: true
  EmptyForBlock:
    active: true
  EmptyFunctionBlock:
    active: true
    ignoreOverridden: false
  EmptyIfBlock:
    active: true
  EmptyInitBlock:
    active: true
  EmptyKtFile:
    active: true
  EmptySecondaryConstructor:
    active: true
  EmptyTryBlock:
    active: true
  EmptyWhenBlock:
    active: true
  EmptyWhileBlock:
    active: true

exceptions:
  active: true
  ExceptionRaisedInUnexpectedLocation:
    active: false
    methodNames: [toString, hashCode, equals, finalize]
  InstanceOfCheckForException:
    active: false
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
  NotImplementedDeclaration:
    active: false
  PrintStackTrace:
    active: false
  RethrowCaughtException:
    active: false
  ReturnFromFinally:
    active: false
    ignoreLabeled: false
  SwallowedException:
    active: false
    ignoredExceptionTypes:
      - InterruptedException
      - NumberFormatException
      - ParseException
      - MalformedURLException
    allowedExceptionNameRegex: "^(_|(ignore|expected).*)"
  ThrowingExceptionFromFinally:
    active: false
  ThrowingExceptionInMain:
    active: false
  ThrowingExceptionsWithoutMessageOrCause:
    active: false
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    exceptions:
      - IllegalArgumentException
      - IllegalStateException
      - IOException
  ThrowingNewInstanceOfSameException:
    active: false
  TooGenericExceptionCaught:
    active: true
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    exceptionNames:
      - ArrayIndexOutOfBoundsException
      - Error
      - Exception
      - IllegalMonitorStateException
      - NullPointerException
      - IndexOutOfBoundsException
      - RuntimeException
      - Throwable
    allowedExceptionNameRegex: "^(_|(ignore|expected).*)"
  TooGenericExceptionThrown:
    active: true
    exceptionNames:
      - Error
      - Exception
      - Throwable
      - RuntimeException

formatting:
  active: true
  android: false
  autoCorrect: true
  AnnotationOnSeparateLine:
    active: false
    autoCorrect: true
  AnnotationSpacing:
    active: false
    autoCorrect: true
  ArgumentListWrapping:
    active: false
    autoCorrect: true
  ChainWrapping:
    active: true
    autoCorrect: true
  CommentSpacing:
    active: true
    autoCorrect: true
  EnumEntryNameCase:
    active: false
    autoCorrect: true
  Filename:
    active: true
  FinalNewline:
    active: true
    autoCorrect: true
    insertFinalNewLine: true
  ImportOrdering:
    active: false
    autoCorrect: true
    layout: '*,java.**,javax.**,kotlin.**,^'
  Indentation:
    active: false
    autoCorrect: true
    indentSize: 4
    continuationIndentSize: 4
  MaximumLineLength:
    active: true
    maxLineLength: 120
  ModifierOrdering:
    active: true
    autoCorrect: true
  MultiLineIfElse:
    active: false
    autoCorrect: true
  NoBlankLineBeforeRbrace:
    active: true
    autoCorrect: true
  NoConsecutiveBlankLines:
    active: true
    autoCorrect: true
  NoEmptyClassBody:
    active: true
    autoCorrect: true
  NoEmptyFirstLineInMethodBlock:
    active: false
    autoCorrect: true
  NoLineBreakAfterElse:
    active: true
    autoCorrect: true
  NoLineBreakBeforeAssignment:
    active: true
    autoCorrect: true
  NoMultipleSpaces:
    active: true
    autoCorrect: true
  NoSemicolons:
    active: true
    autoCorrect: true
  NoTrailingSpaces:
    active: true
    autoCorrect: true
  NoUnitReturn:
    active: true
    autoCorrect: true
  NoUnusedImports:
    active: true
    autoCorrect: true
  NoWildcardImports:
    active: true
  PackageName:
    active: true
    autoCorrect: true
  ParameterListWrapping:
    active: false
    autoCorrect: true
    indentSize: 4
  SpacingAroundAngleBrackets:
    active: false
    autoCorrect: true
  SpacingAroundColon:
    active: true
    autoCorrect: true
  SpacingAroundComma:
    active: true
    autoCorrect: true
  SpacingAroundCurly:
    active: true
    autoCorrect: true
  SpacingAroundDot:
    active: true
    autoCorrect: true
  SpacingAroundDoubleColon:
    active: false
    autoCorrect: true
  SpacingAroundKeyword:
    active: true
    autoCorrect: true
  SpacingAroundOperators:
    active: true
    autoCorrect: true
  SpacingAroundParens:
    active: true
    autoCorrect: true
  SpacingAroundRangeOperator:
    active: true
    autoCorrect: true
  SpacingAroundUnaryOperator:
    active: false
    autoCorrect: true
  SpacingBetweenDeclarationsWithAnnotations:
    active: false
    autoCorrect: true
  SpacingBetweenDeclarationsWithComments:
    active: false
    autoCorrect: true
  StringTemplate:
    active: true
    autoCorrect: true

naming:
  active: true
  ClassNaming:
    active: true
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    classPattern: '[A-Z][a-zA-Z0-9]*'
  ConstructorParameterNaming:
    active: true
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    parameterPattern: '[a-z][A-Za-z0-9]*'
    privateParameterPattern: '[a-z][A-Za-z0-9]*'
    excludeClassPattern: '$^'
    ignoreOverridden: true
  EnumNaming:
    active: true
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    enumEntryPattern: '[A-Z][_a-zA-Z0-9]*'
  ForbiddenClassName:
    active: false
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    forbiddenName: []
  FunctionMaxLength:
    active: false
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    maximumFunctionNameLength: 30
  FunctionMinLength:
    active: false
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    minimumFunctionNameLength: 3
  FunctionNaming:
    active: true
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    functionPattern: '([a-z][a-zA-Z0-9]*)|(`.*`)'
    excludeClassPattern: '$^'
    ignoreOverridden: true
    ignoreAnnotated: ['Composable']
  FunctionParameterNaming:
    active: true
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    parameterPattern: '[a-z][A-Za-z0-9]*'
    excludeClassPattern: '$^'
    ignoreOverridden: true
  InvalidPackageDeclaration:
    active: false
    rootPackage: ''
  MatchingDeclarationName:
    active: true
    mustBeFirst: true
  MemberNameEqualsClassName:
    active: true
    ignoreOverridden: true
  ObjectPropertyNaming:
    active: true
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    constantPattern: '[A-Za-z][_A-Za-z0-9]*'
    propertyPattern: '[A-Za-z][_A-Za-z0-9]*'
    privatePropertyPattern: '(_)?[A-Za-z][_A-Za-z0-9]*'
  PackageNaming:
    active: true
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    packagePattern: '[a-z]+(\.[a-z][A-Za-z0-9]*)*'
  TopLevelPropertyNaming:
    active: true
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    constantPattern: '[A-Z][_A-Z0-9]*'
    propertyPattern: '[A-Za-z][_A-Za-z0-9]*'
    privatePropertyPattern: '_?[A-Za-z][_A-Za-z0-9]*'
  VariableMaxLength:
    active: false
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    maximumVariableNameLength: 64
  VariableMinLength:
    active: false
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    minimumVariableNameLength: 1
  VariableNaming:
    active: true
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    variablePattern: '[a-z][A-Za-z0-9]*'
    privateVariablePattern: '(_)?[a-z][A-Za-z0-9]*'
    excludeClassPattern: '$^'
    ignoreOverridden: true

performance:
  active: true
  ArrayPrimitive:
    active: true
  ForEachOnRange:
    active: true
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
  SpreadOperator:
    active: true
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
  UnnecessaryTemporaryInstantiation:
    active: true

potential-bugs:
  active: true
  Deprecation:
    active: false
  DuplicateCaseInWhenExpression:
    active: true
  EqualsAlwaysReturnsTrueOrFalse:
    active: true
  EqualsWithHashCodeExist:
    active: true
  ExplicitGarbageCollectionCall:
    active: true
  HasPlatformType:
    active: false
  IgnoredReturnValue:
    active: false
    restrictToAnnotatedMethods: true
    returnValueAnnotations: ['*.CheckReturnValue', '*.CheckResult']
  ImplicitDefaultLocale:
    active: false
  ImplicitUnitReturnType:
    active: false
    allowExplicitReturnType: true
  InvalidRange:
    active: true
  IteratorHasNextCallsNextMethod:
    active: true
  IteratorNotThrowingNoSuchElementException:
    active: true
  LateinitUsage:
    active: false
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    excludeAnnotatedProperties: []
    ignoreOnClassesPattern: ''
  MapGetWithNotNullAssertionOperator:
    active: false
  MissingWhenCase:
    active: true
    allowElseExpression: true
  NullableToStringCall:
    active: false
  RedundantElseInWhen:
    active: true
  UnconditionalJumpStatementInLoop:
    active: false
  UnnecessaryNotNullOperator:
    active: true
  UnnecessarySafeCall:
    active: true
  UnreachableCode:
    active: true
  UnsafeCallOnNullableType:
    active: true
  UnsafeCast:
    active: true
  UselessPostfixExpression:
    active: true
  WrongEqualsTypeParameter:
    active: true

style:
  active: true
  CollapsibleIfStatements:
    active: false
  DataClassContainsFunction:
    active: false
    conversionFunctionPrefix: 'to'
  DataClassShouldBeImmutable:
    active: false
  DestructuringDeclarationWithTooManyEntries:
    active: true
    maxDestructuringEntries: 3
  EqualsNullCall:
    active: true
  EqualsOnSignatureLine:
    active: false
  ExplicitCollectionElementAccessMethod:
    active: false
  ExplicitItLambdaParameter:
    active: false
  ExpressionBodySyntax:
    active: false
    includeLineWrapping: false
  ForbiddenComment:
    active: true
    values: ['TODO:', 'FIXME:', 'STOPSHIP:']
    allowedPatterns: ''
  ForbiddenImport:
    active: false
    imports: []
    forbiddenPatterns: ''
  ForbiddenMethodCall:
    active: false
    methods: ['kotlin.io.println', 'kotlin.io.print']
  ForbiddenPublicDataClass:
    active: false
    excludes: ['**']
    ignorePackages: ['*.internal', '*.internal.*']
  ForbiddenVoid:
    active: false
    ignoreOverridden: false
    ignoreUsageInGenerics: false
  FunctionOnlyReturningConstant:
    active: true
    ignoreOverridableFunction: true
    excludedFunctions: 'describeContents'
    excludeAnnotatedFunction: ['dagger.Provides']
  LibraryCodeMustSpecifyReturnType:
    active: true
    excludes: ['**']
  LibraryEntitiesShouldNotBePublic:
    active: false
    excludes: ['**']
  LoopWithTooManyJumpStatements:
    active: true
    maxJumpCount: 1
  MagicNumber:
    active: true
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    ignoreNumbers: ['-1', '0', '1', '2']
    ignoreHashCodeFunction: true
    ignorePropertyDeclaration: false
    ignoreLocalVariableDeclaration: false
    ignoreConstantDeclaration: true
    ignoreCompanionObjectPropertyDeclaration: true
    ignoreAnnotation: false
    ignoreNamedArgument: true
    ignoreEnums: false
    ignoreRanges: false
    ignoreExtensionFunctions: true
  MandatoryBracesIfStatements:
    active: false
  MandatoryBracesLoops:
    active: false
  MaxLineLength:
    active: true
    maxLineLength: 120
    excludePackageStatements: true
    excludeImportStatements: true
    excludeCommentStatements: false
  MayBeConst:
    active: true
  ModifierOrder:
    active: true
  NestedClassesVisibility:
    active: true
  NewLineAtEndOfFile:
    active: true
  NoTabs:
    active: false
  OptionalAbstractKeyword:
    active: true
  OptionalUnit:
    active: false
  OptionalWhenBraces:
    active: false
  PreferToOverPairSyntax:
    active: false
  ProtectedMemberInFinalClass:
    active: true
  RedundantExplicitType:
    active: false
  RedundantHigherOrderMapUsage:
    active: true
  RedundantVisibilityModifierRule:
    active: false
  ReturnCount:
    active: true
    max: 2
    excludedFunctions: "equals"
    excludeLabeled: false
    excludeReturnFromLambda: true
    excludeGuardClauses: false
  SafeCast:
    active: true
  SerialVersionUIDInSerializableClass:
    active: false
  SpacingBetweenPackageAndImports:
    active: false
  ThrowsCount:
    active: true
    max: 2
    excludeGuardClauses: false
  TrailingWhitespace:
    active: false
  UnderscoresInNumericLiterals:
    active: false
    acceptableDecimalLength: 5
  UnnecessaryAbstractClass:
    active: true
    excludeAnnotatedClasses: ['dagger.Module']
  UnnecessaryAnnotationUseSiteTarget:
    active: false
  UnnecessaryApply:
    active: true
  UnnecessaryFilter:
    active: true
  UnnecessaryInheritance:
    active: true
  UnnecessaryLet:
    active: false
  UnnecessaryParentheses:
    active: false
  UntilInsteadOfRangeTo:
    active: false
  UnusedImports:
    active: false
  UnusedPrivateClass:
    active: true
  UnusedPrivateMember:
    active: true
    allowedNames: "(_|ignored|expected|serialVersionUID)"
  UseArrayLiteralsInAnnotations:
    active: false
  UseCheckOrError:
    active: false
  UseDataClass:
    active: false
    excludeAnnotatedClasses: []
    allowVars: false
  UseEmptyCounterpart:
    active: false
  UseIfEmptyOrIfBlank:
    active: false
  UseIfInsteadOfWhen:
    active: false
  UseIsNullOrEmpty:
    active: false
  UseOrEmpty:
    active: false
  UseRequire:
    active: false
  UseRequireNotNull:
    active: false
  UselessCallOnNotNull:
    active: true
  UtilityClassWithPublicConstructor:
    active: true
  VarCouldBeVal:
    active: true
  WildcardImport:
    active: true
    excludes: ['**/test/**', '**/androidTest/**', '**/commonTest/**', '**/jvmTest/**', '**/jsTest/**', '**/iosTest/**']
    excludeImports: ['java.util.*', 'kotlinx.android.synthetic.*']
```

## 5. CI/CD配置

### 5.1 GitHub Actions工作流
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  JAVA_VERSION: '17'
  NODE_VERSION: '18'

jobs:
  test-backend:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:8
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up JDK
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
    
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
    
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
    
    - name: Run Ktlint
      run: ./gradlew ktlintCheck
    
    - name: Run Detekt
      run: ./gradlew detekt
    
    - name: Run tests
      run: ./gradlew test
      env:
        SPRING_DATASOURCE_URL: ****************************************
        SPRING_DATASOURCE_USERNAME: postgres
        SPRING_DATASOURCE_PASSWORD: postgres
        SPRING_REDIS_HOST: localhost
        SPRING_REDIS_PORT: 6379
    
    - name: Generate test report
      run: ./gradlew jacocoTestReport
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./build/reports/jacoco/test/jacocoTestReport.xml
        flags: backend
        name: backend-coverage
    
    - name: SonarQube Scan
      uses: sonarqube-quality-gate-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  test-frontend:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Run ESLint
      run: |
        cd frontend
        npm run lint
    
    - name: Run type check
      run: |
        cd frontend
        npm run type-check
    
    - name: Run tests
      run: |
        cd frontend
        npm run test:ci
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  build-and-deploy:
    needs: [test-backend, test-frontend]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up JDK
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Build backend
      run: ./gradlew build -x test
    
    - name: Build frontend
      run: |
        cd frontend
        npm ci
        npm run build
    
    - name: Build Docker images
      run: |
        docker build -t procurement-platform-backend .
        docker build -t procurement-platform-frontend ./frontend
    
    - name: Deploy to staging
      run: |
        # 部署脚本
        echo "Deploying to staging environment..."
        # 这里添加实际的部署逻辑
```

## 8. TDD工具链配置

### 8.1 IntelliJ IDEA TDD配置

#### TDD插件安装
```xml
<!-- .idea/externalDependencies.xml -->
<component name="ExternalDependencies">
  <dependency groupId="org.jetbrains.plugins" artifactId="coverage" version="LATEST" />
  <dependency groupId="com.intellij" artifactId="junit" version="LATEST" />
  <dependency groupId="org.jetbrains.kotlin" artifactId="kotlin-test" version="LATEST" />
</component>
```

#### TDD Live Templates配置
```xml
<!-- .idea/templates/TDD.xml -->
<templateSet group="TDD">
  <template name="tdd" value="@Test&#10;fun `should $EXPECTED$ when $CONDITION$`() {&#10;    // Given&#10;    $GIVEN$&#10;    &#10;    // When&#10;    $WHEN$&#10;    &#10;    // Then&#10;    $THEN$&#10;}" description="TDD测试模板" toReformat="true" toShortenFQNames="true">
    <variable name="EXPECTED" expression="" defaultValue="&quot;expected_result&quot;" alwaysStopAt="true" />
    <variable name="CONDITION" expression="" defaultValue="&quot;condition&quot;" alwaysStopAt="true" />
    <variable name="GIVEN" expression="" defaultValue="&quot;// 准备测试数据&quot;" alwaysStopAt="true" />
    <variable name="WHEN" expression="" defaultValue="&quot;// 执行被测试方法&quot;" alwaysStopAt="true" />
    <variable name="THEN" expression="" defaultValue="&quot;// 验证结果&quot;" alwaysStopAt="true" />
    <context>
      <option name="KOTLIN" value="true" />
    </context>
  </template>

  <template name="mock" value="private val mock$NAME$ = mockk&lt;$TYPE$&gt;()" description="Mock对象模板" toReformat="true" toShortenFQNames="true">
    <variable name="NAME" expression="" defaultValue="&quot;Service&quot;" alwaysStopAt="true" />
    <variable name="TYPE" expression="" defaultValue="&quot;ServiceType&quot;" alwaysStopAt="true" />
    <context>
      <option name="KOTLIN" value="true" />
    </context>
  </template>
</templateSet>
```

#### TDD运行配置
```xml
<!-- .idea/runConfigurations/TDD_All_Tests.xml -->
<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="TDD All Tests" type="JUnit" factoryName="JUnit">
    <option name="PACKAGE_NAME" value="" />
    <option name="MAIN_CLASS_NAME" value="" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="package" />
    <option name="VM_PARAMETERS" value="-ea -Dspring.profiles.active=test" />
    <option name="PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$MODULE_DIR$" />
    <option name="PASS_PARENT_ENVS" value="true" />
    <option name="TEST_SEARCH_SCOPE">
      <value defaultName="singleModule" />
    </option>
    <patterns />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>
```

### 8.2 Gradle TDD配置

#### 测试依赖配置
```kotlin
// build.gradle.kts
dependencies {
    // TDD核心依赖
    testImplementation("org.junit.jupiter:junit-jupiter:5.9.2")
    testImplementation("org.assertj:assertj-core:3.24.2")
    testImplementation("io.mockk:mockk:1.13.4")

    // Spring Boot测试
    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        exclude(group = "org.junit.vintage", module = "junit-vintage-engine")
        exclude(group = "org.mockito", module = "mockito-core")
    }

    // 集成测试
    testImplementation("org.testcontainers:junit-jupiter:1.17.6")
    testImplementation("org.testcontainers:postgresql:1.17.6")

    // 测试覆盖率
    testImplementation("org.jacoco:org.jacoco.agent:0.8.8")
}

// TDD测试配置
tasks.test {
    useJUnitPlatform()

    // TDD快速反馈配置
    maxParallelForks = Runtime.getRuntime().availableProcessors()
    forkEvery = 100

    // 测试输出配置
    testLogging {
        events("passed", "skipped", "failed")
        exceptionFormat = org.gradle.api.tasks.testing.logging.TestExceptionFormat.FULL
        showStandardStreams = false
    }

    // 测试超时配置
    systemProperty("junit.jupiter.execution.timeout.default", "5s")
    systemProperty("junit.jupiter.execution.timeout.testable.method.default", "5s")
}

// JaCoCo覆盖率配置
jacoco {
    toolVersion = "0.8.8"
}

tasks.jacocoTestReport {
    dependsOn(tasks.test)

    reports {
        xml.required.set(true)
        html.required.set(true)
        csv.required.set(false)
    }

    executionData.setFrom(fileTree(buildDir).include("**/jacoco/*.exec"))
}

tasks.jacocoTestCoverageVerification {
    dependsOn(tasks.jacocoTestReport)

    violationRules {
        rule {
            limit {
                counter = "LINE"
                value = "COVEREDRATIO"
                minimum = "0.95".toBigDecimal()
            }
        }
        rule {
            limit {
                counter = "BRANCH"
                value = "COVEREDRATIO"
                minimum = "0.90".toBigDecimal()
            }
        }
    }
}

// TDD任务配置
tasks.register("tdd") {
    group = "verification"
    description = "Run TDD cycle: test -> build -> coverage"

    dependsOn(tasks.test, tasks.jacocoTestCoverageVerification)
}
```

### 8.3 前端TDD配置

#### Vitest配置
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  test: {
    // TDD环境配置
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./src/test/setup.ts'],

    // TDD快速反馈配置
    watch: true,
    reporter: ['verbose', 'html'],
    outputFile: './coverage/test-results.html',

    // 覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'html', 'lcov'],
      reportsDirectory: './coverage',
      thresholds: {
        lines: 80,
        functions: 80,
        branches: 80,
        statements: 80
      },
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*'
      ]
    },

    // 测试超时配置
    testTimeout: 5000,
    hookTimeout: 10000
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src')
    }
  }
})
```

#### 测试工具配置
```typescript
// src/test/setup.ts
import { config } from '@vue/test-utils'
import { vi } from 'vitest'

// 全局测试配置
config.global.plugins = []

// Mock全局对象
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
vi.stubGlobal('localStorage', localStorageMock)
```

### 8.4 TDD CI/CD配置

#### GitHub Actions TDD工作流
```yaml
# .github/workflows/tdd.yml
name: TDD Workflow

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  backend-tdd:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_DB: procurement_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3

    - name: Set up JDK 21
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: Run TDD Tests
      run: ./gradlew test jacocoTestReport
      env:
        SPRING_DATASOURCE_URL: *************************************************
        SPRING_DATASOURCE_USERNAME: postgres
        SPRING_DATASOURCE_PASSWORD: test

    - name: Upload Coverage Reports
      uses: codecov/codecov-action@v3
      with:
        file: ./build/reports/jacoco/test/jacocoTestReport.xml
        flags: backend
        name: backend-coverage

    - name: Quality Gate Check
      run: ./gradlew jacocoTestCoverageVerification

  frontend-tdd:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci

    - name: Run TDD Tests
      working-directory: ./frontend
      run: npm run test:coverage

    - name: Upload Coverage Reports
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  e2e-tests:
    needs: [backend-tdd, frontend-tdd]
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: e2e/package-lock.json

    - name: Install Playwright
      working-directory: ./e2e
      run: |
        npm ci
        npx playwright install --with-deps

    - name: Run E2E Tests
      working-directory: ./e2e
      run: npm run test

    - name: Upload E2E Results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: playwright-report
        path: e2e/playwright-report/
```

### 8.5 TDD开发环境优化

#### 快速测试运行脚本
```bash
#!/bin/bash
# scripts/tdd-watch.sh

echo "🚀 启动TDD监控模式..."

# 后端测试监控
echo "📱 启动后端测试监控..."
./gradlew test --continuous &
BACKEND_PID=$!

# 前端测试监控
echo "🌐 启动前端测试监控..."
cd frontend && npm run test:watch &
FRONTEND_PID=$!

# 清理函数
cleanup() {
    echo "🛑 停止TDD监控..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    exit 0
}

# 捕获退出信号
trap cleanup SIGINT SIGTERM

echo "✅ TDD监控已启动，按Ctrl+C停止"
echo "📊 后端测试PID: $BACKEND_PID"
echo "📊 前端测试PID: $FRONTEND_PID"

# 等待
wait
```

#### TDD性能监控
```kotlin
// src/test/kotlin/TDDPerformanceMonitor.kt
class TDDPerformanceMonitor {
    companion object {
        private val testExecutionTimes = mutableMapOf<String, Long>()

        @JvmStatic
        @BeforeEach
        fun startTimer(testInfo: TestInfo) {
            testExecutionTimes[testInfo.displayName] = System.currentTimeMillis()
        }

        @JvmStatic
        @AfterEach
        fun endTimer(testInfo: TestInfo) {
            val startTime = testExecutionTimes[testInfo.displayName] ?: return
            val executionTime = System.currentTimeMillis() - startTime

            if (executionTime > 1000) { // 超过1秒的测试
                println("⚠️  慢测试警告: ${testInfo.displayName} 耗时 ${executionTime}ms")
            }
        }
    }
}
```

这套TDD工具链配置为超级个人全栈开发者提供了完整的测试驱动开发环境，通过IntelliJ IDEA的深度配置、完善的TDD工具集成、自动化的测试流水线，最大化提升了TDD开发效率和代码质量，确保红-绿-重构循环的快速反馈。
