# 超级个人全栈开发 - 日志管理规范文档

## 1. 日志管理总览

### 1.1 日志管理设计理念
- **结构化日志**：采用JSON格式，便于解析和查询
- **分级管理**：合理使用日志级别，避免日志泛滥
- **上下文追踪**：通过TraceID实现分布式链路追踪
- **敏感信息保护**：自动脱敏敏感数据，保护用户隐私
- **性能优化**：异步日志写入，不影响业务性能
- **可观测性**：日志与监控指标结合，提供完整的系统可观测性

### 1.2 日志架构图
```mermaid
graph TB
    subgraph "应用层 (Application Layer)"
        BACKEND[后端应用<br/>Spring Boot + Logback]
        FRONTEND[前端应用<br/>Vue 3 + Console/Remote]
        NGINX[Nginx<br/>访问日志]
    end
    
    subgraph "日志收集层 (Log Collection)"
        FILEBEAT[Filebeat<br/>日志采集]
        FLUENTD[Fluentd<br/>日志处理]
        LOGSTASH[Logstash<br/>日志解析]
    end
    
    subgraph "日志存储层 (Log Storage)"
        ELASTICSEARCH[Elasticsearch<br/>日志存储和索引]
        S3[对象存储<br/>长期归档]
    end
    
    subgraph "日志分析层 (Log Analysis)"
        KIBANA[Kibana<br/>日志查询和可视化]
        GRAFANA[Grafana<br/>日志监控面板]
        ALERT[AlertManager<br/>日志告警]
    end
    
    subgraph "日志类型 (Log Types)"
        ACCESS_LOG[访问日志<br/>HTTP请求记录]
        APP_LOG[应用日志<br/>业务逻辑记录]
        ERROR_LOG[错误日志<br/>异常和错误记录]
        AUDIT_LOG[审计日志<br/>重要操作记录]
        PERF_LOG[性能日志<br/>性能指标记录]
    end
    
    BACKEND --> FILEBEAT
    FRONTEND --> FILEBEAT
    NGINX --> FILEBEAT
    
    FILEBEAT --> FLUENTD
    FLUENTD --> LOGSTASH
    LOGSTASH --> ELASTICSEARCH
    
    ELASTICSEARCH --> KIBANA
    ELASTICSEARCH --> GRAFANA
    ELASTICSEARCH --> ALERT
    ELASTICSEARCH --> S3
```

## 2. 后端日志配置

### 2.1 Logback配置
```xml
<!-- logback-spring.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 日志格式定义 -->
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] %logger{36} - %msg%n"/>
    <property name="JSON_PATTERN" value='{"timestamp":"%d{yyyy-MM-dd HH:mm:ss.SSS}","level":"%level","thread":"%thread","traceId":"%X{traceId:-}","logger":"%logger{36}","message":"%msg","exception":"%ex"}%n'/>
    
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- 应用日志文件 -->
    <appender name="APP_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/application.log</file>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <mdc/>
                <message/>
                <stackTrace/>
                <pattern>
                    <pattern>
                        {
                            "traceId": "%X{traceId:-}",
                            "spanId": "%X{spanId:-}",
                            "userId": "%X{userId:-}",
                            "requestId": "%X{requestId:-}",
                            "service": "procurement-platform"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/application.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 错误日志文件 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/error.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <mdc/>
                <message/>
                <stackTrace/>
                <pattern>
                    <pattern>
                        {
                            "traceId": "%X{traceId:-}",
                            "errorType": "APPLICATION_ERROR",
                            "service": "procurement-platform"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
    </appender>
    
    <!-- 审计日志文件 -->
    <appender name="AUDIT_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/audit.log</file>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <mdc/>
                <message/>
                <pattern>
                    <pattern>
                        {
                            "logType": "AUDIT",
                            "service": "procurement-platform"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/audit.%d{yyyy-MM-dd}.log.gz</fileNamePattern>
            <maxHistory>365</maxHistory>
        </rollingPolicy>
    </appender>
    
    <!-- 异步日志配置 -->
    <appender name="ASYNC_APP" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="APP_FILE"/>
        <queueSize>1024</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>true</includeCallerData>
    </appender>
    
    <appender name="ASYNC_ERROR" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ERROR_FILE"/>
        <queueSize>256</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>true</includeCallerData>
    </appender>
    
    <!-- 特定Logger配置 -->
    <logger name="com.procurement.platform" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_APP"/>
        <appender-ref ref="ASYNC_ERROR"/>
    </logger>
    
    <logger name="AUDIT" level="INFO" additivity="false">
        <appender-ref ref="AUDIT_FILE"/>
    </logger>
    
    <!-- SQL日志 -->
    <logger name="org.hibernate.SQL" level="DEBUG"/>
    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE"/>
    
    <!-- 根Logger -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_APP"/>
        <appender-ref ref="ASYNC_ERROR"/>
    </root>
    
    <!-- 开发环境配置 -->
    <springProfile name="dev">
        <logger name="com.procurement.platform" level="DEBUG"/>
        <logger name="org.springframework.web" level="DEBUG"/>
    </springProfile>
    
    <!-- 生产环境配置 -->
    <springProfile name="prod">
        <logger name="com.procurement.platform" level="INFO"/>
        <logger name="org.springframework" level="WARN"/>
        <logger name="org.hibernate" level="WARN"/>
    </springProfile>
</configuration>
```

### 2.2 日志级别使用规范
```kotlin
// 日志级别使用示例
@Service
class RequirementApplicationService {
    
    private val log = LoggerFactory.getLogger(RequirementApplicationService::class.java)
    
    fun createRequirement(command: CreateRequirementCommand): RequirementId {
        // DEBUG: 详细的调试信息，仅在开发环境使用
        log.debug("Creating requirement with command: {}", command)
        
        try {
            // INFO: 重要的业务操作记录
            log.info("Creating procurement requirement for buyer: {}", command.buyerId)
            
            val requirement = ProcurementRequirement.create(command)
            requirementRepository.save(requirement)
            
            // INFO: 成功操作的记录
            log.info("Successfully created requirement with ID: {}", requirement.id)
            
            return requirement.id
            
        } catch (DomainException ex) {
            // WARN: 业务异常，预期内的错误
            log.warn("Business rule violation when creating requirement: {}", ex.message)
            throw ex
            
        } catch (Exception ex) {
            // ERROR: 系统异常，需要立即关注
            log.error("Unexpected error when creating requirement for buyer: {}", 
                command.buyerId, ex)
            throw ex
        }
    }
}
```

### 2.3 MDC上下文管理
```kotlin
// TraceID过滤器
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
class TraceIdFilter : OncePerRequestFilter() {
    
    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain
    ) {
        val traceId = request.getHeader("X-Trace-ID") ?: UUID.randomUUID().toString()
        val requestId = UUID.randomUUID().toString()
        
        try {
            // 设置MDC上下文
            MDC.put("traceId", traceId)
            MDC.put("requestId", requestId)
            MDC.put("requestUri", request.requestURI)
            MDC.put("requestMethod", request.method)
            
            // 从Security Context获取用户信息
            val authentication = SecurityContextHolder.getContext().authentication
            if (authentication != null && authentication.isAuthenticated) {
                MDC.put("userId", authentication.name)
            }
            
            // 添加响应头
            response.setHeader("X-Trace-ID", traceId)
            response.setHeader("X-Request-ID", requestId)
            
            filterChain.doFilter(request, response)
            
        } finally {
            // 清理MDC上下文
            MDC.clear()
        }
    }
}
```

## 3. 前端日志配置

### 3.1 前端日志工具
```typescript
// logger.ts - 前端日志工具
interface LogLevel {
  DEBUG: 0
  INFO: 1
  WARN: 2
  ERROR: 3
}

interface LogEntry {
  timestamp: string
  level: keyof LogLevel
  message: string
  data?: any
  traceId?: string
  userId?: string
  url?: string
  userAgent?: string
}

class Logger {
  private level: keyof LogLevel = 'INFO'
  private remoteEndpoint?: string
  private buffer: LogEntry[] = []
  private maxBufferSize = 100
  
  constructor(config?: { level?: keyof LogLevel; remoteEndpoint?: string }) {
    this.level = config?.level || 'INFO'
    this.remoteEndpoint = config?.remoteEndpoint
    
    // 定期发送日志到服务器
    setInterval(() => this.flush(), 30000)
    
    // 页面卸载时发送剩余日志
    window.addEventListener('beforeunload', () => this.flush())
  }
  
  debug(message: string, data?: any) {
    this.log('DEBUG', message, data)
  }
  
  info(message: string, data?: any) {
    this.log('INFO', message, data)
  }
  
  warn(message: string, data?: any) {
    this.log('WARN', message, data)
  }
  
  error(message: string, data?: any) {
    this.log('ERROR', message, data)
    // 错误日志立即发送
    this.flush()
  }
  
  private log(level: keyof LogLevel, message: string, data?: any) {
    const logEntry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
      traceId: this.getTraceId(),
      userId: this.getUserId(),
      url: window.location.href,
      userAgent: navigator.userAgent
    }
    
    // 控制台输出
    this.consoleLog(logEntry)
    
    // 添加到缓冲区
    this.buffer.push(logEntry)
    
    // 缓冲区满时发送
    if (this.buffer.length >= this.maxBufferSize) {
      this.flush()
    }
  }
  
  private consoleLog(entry: LogEntry) {
    const { level, message, data } = entry
    const prefix = `[${entry.timestamp}] [${level}]`
    
    switch (level) {
      case 'DEBUG':
        console.debug(prefix, message, data)
        break
      case 'INFO':
        console.info(prefix, message, data)
        break
      case 'WARN':
        console.warn(prefix, message, data)
        break
      case 'ERROR':
        console.error(prefix, message, data)
        break
    }
  }
  
  private async flush() {
    if (this.buffer.length === 0 || !this.remoteEndpoint) return
    
    const logs = [...this.buffer]
    this.buffer = []
    
    try {
      await fetch(this.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ logs })
      })
    } catch (error) {
      console.error('Failed to send logs to server:', error)
      // 发送失败时重新加入缓冲区
      this.buffer.unshift(...logs)
    }
  }
  
  private getTraceId(): string {
    // 从HTTP响应头或本地存储获取TraceID
    return sessionStorage.getItem('traceId') || ''
  }
  
  private getUserId(): string {
    // 从用户状态获取用户ID
    return localStorage.getItem('userId') || ''
  }
}

// 创建全局日志实例
export const logger = new Logger({
  level: import.meta.env.DEV ? 'DEBUG' : 'INFO',
  remoteEndpoint: '/api/logs'
})
```

### 3.2 Vue应用日志集成
```typescript
// main.ts - Vue应用日志配置
import { createApp } from 'vue'
import { logger } from './utils/logger'
import App from './App.vue'

const app = createApp(App)

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  logger.error('Vue应用错误', {
    error: {
      message: err.message,
      stack: err.stack
    },
    component: instance?.$options.name,
    errorInfo: info
  })
}

// 全局警告处理
app.config.warnHandler = (msg, instance, trace) => {
  logger.warn('Vue应用警告', {
    message: msg,
    component: instance?.$options.name,
    trace
  })
}

// 路由变化日志
router.beforeEach((to, from) => {
  logger.info('路由导航', {
    from: from.path,
    to: to.path,
    params: to.params,
    query: to.query
  })
})

// API请求日志
axios.interceptors.request.use(
  (config) => {
    logger.debug('API请求', {
      method: config.method,
      url: config.url,
      params: config.params,
      data: config.data
    })
    return config
  },
  (error) => {
    logger.error('API请求失败', { error: error.message })
    return Promise.reject(error)
  }
)

axios.interceptors.response.use(
  (response) => {
    logger.debug('API响应', {
      status: response.status,
      url: response.config.url,
      data: response.data
    })
    return response
  },
  (error) => {
    logger.error('API响应错误', {
      status: error.response?.status,
      url: error.config?.url,
      message: error.message,
      data: error.response?.data
    })
    return Promise.reject(error)
  }
)
```

## 4. 敏感信息脱敏

### 4.1 后端敏感信息脱敏
```kotlin
// 敏感信息脱敏工具
@Component
class SensitiveDataMasker {

    private val phonePattern = Regex("(\\d{3})\\d{4}(\\d{4})")
    private val emailPattern = Regex("([^@]{1,3})[^@]*(@.*)")
    private val idCardPattern = Regex("(\\d{6})\\d{8}(\\d{4})")
    private val bankCardPattern = Regex("(\\d{4})\\d{8,12}(\\d{4})")

    fun maskPhone(phone: String?): String? {
        return phone?.replace(phonePattern, "$1****$2")
    }

    fun maskEmail(email: String?): String? {
        return email?.replace(emailPattern, "$1***$2")
    }

    fun maskIdCard(idCard: String?): String? {
        return idCard?.replace(idCardPattern, "$1********$2")
    }

    fun maskBankCard(bankCard: String?): String? {
        return bankCard?.replace(bankCardPattern, "$1****$2")
    }

    fun maskObject(obj: Any): Any {
        return when (obj) {
            is String -> maskSensitiveString(obj)
            is Map<*, *> -> obj.mapValues { (_, value) ->
                if (value != null) maskObject(value) else null
            }
            is List<*> -> obj.map { if (it != null) maskObject(it) else null }
            else -> obj
        }
    }

    private fun maskSensitiveString(str: String): String {
        return when {
            str.contains("password", ignoreCase = true) -> "***"
            str.contains("token", ignoreCase = true) -> "***"
            str.contains("secret", ignoreCase = true) -> "***"
            phonePattern.matches(str) -> maskPhone(str) ?: str
            emailPattern.matches(str) -> maskEmail(str) ?: str
            else -> str
        }
    }
}

// 日志脱敏拦截器
@Component
class LogMaskingInterceptor : HandlerInterceptor {

    @Autowired
    private lateinit var sensitiveDataMasker: SensitiveDataMasker

    override fun preHandle(
        request: HttpServletRequest,
        response: HttpServletResponse,
        handler: Any
    ): Boolean {
        val requestBody = getRequestBody(request)
        val maskedBody = sensitiveDataMasker.maskObject(requestBody)

        log.info("HTTP请求: {} {} - Body: {}",
            request.method, request.requestURI, maskedBody)

        return true
    }

    override fun afterCompletion(
        request: HttpServletRequest,
        response: HttpServletResponse,
        handler: Any,
        ex: Exception?
    ) {
        log.info("HTTP响应: {} {} - Status: {}",
            request.method, request.requestURI, response.status)
    }
}
```

### 4.2 前端敏感信息脱敏
```typescript
// 前端敏感信息脱敏
class SensitiveDataMasker {
  static maskPhone(phone: string): string {
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }

  static maskEmail(email: string): string {
    return email.replace(/([^@]{1,3})[^@]*(@.*)/, '$1***$2')
  }

  static maskIdCard(idCard: string): string {
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
  }

  static maskObject(obj: any): any {
    if (typeof obj !== 'object' || obj === null) {
      return obj
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.maskObject(item))
    }

    const masked: any = {}
    for (const [key, value] of Object.entries(obj)) {
      if (this.isSensitiveField(key)) {
        masked[key] = '***'
      } else if (typeof value === 'object') {
        masked[key] = this.maskObject(value)
      } else {
        masked[key] = value
      }
    }

    return masked
  }

  private static isSensitiveField(fieldName: string): boolean {
    const sensitiveFields = [
      'password', 'token', 'secret', 'key', 'credential',
      'phone', 'email', 'idCard', 'bankCard'
    ]

    return sensitiveFields.some(field =>
      fieldName.toLowerCase().includes(field)
    )
  }
}

// 在日志记录时使用脱敏
logger.info('用户信息', SensitiveDataMasker.maskObject(userInfo))
```

## 5. 日志监控和告警

### 5.1 日志监控配置
```yaml
# prometheus.yml - 日志监控配置
global:
  scrape_interval: 15s

rule_files:
  - "log_alerts.yml"

scrape_configs:
  - job_name: 'application-logs'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/actuator/prometheus'

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### 5.2 日志告警规则
```yaml
# log_alerts.yml - 日志告警规则
groups:
  - name: log_alerts
    rules:
      # 错误日志告警
      - alert: HighErrorRate
        expr: rate(logback_events_total{level="error"}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "应用错误率过高"
          description: "过去5分钟内错误日志率超过0.1/秒"

      # 异常日志告警
      - alert: UnexpectedException
        expr: increase(logback_events_total{level="error"}[1m]) > 5
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "异常日志激增"
          description: "1分钟内错误日志增加超过5条"

      # 日志文件大小告警
      - alert: LogFileSizeHigh
        expr: log_file_size_bytes > 1000000000  # 1GB
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "日志文件过大"
          description: "日志文件大小超过1GB"
```

## 6. 日志最佳实践

### 6.1 日志记录原则
```kotlin
// 好的日志记录示例
@Service
class OrderService {

    private val log = LoggerFactory.getLogger(OrderService::class.java)

    fun processOrder(orderId: OrderId) {
        // 1. 记录方法入口，包含关键参数
        log.info("开始处理订单: orderId={}", orderId)

        try {
            val order = orderRepository.findById(orderId)
                ?: throw OrderNotFoundException("订单不存在: $orderId")

            // 2. 记录重要的业务状态变化
            log.info("订单状态变更: orderId={}, from={}, to={}",
                orderId, order.status, OrderStatus.PROCESSING)

            order.process()
            orderRepository.save(order)

            // 3. 记录成功结果
            log.info("订单处理完成: orderId={}, status={}",
                orderId, order.status)

        } catch (DomainException ex) {
            // 4. 记录业务异常，包含上下文信息
            log.warn("订单处理业务异常: orderId={}, error={}",
                orderId, ex.message)
            throw ex

        } catch (Exception ex) {
            // 5. 记录系统异常，包含完整堆栈
            log.error("订单处理系统异常: orderId={}", orderId, ex)
            throw ex
        }
    }
}
```

### 6.2 性能敏感场景日志
```kotlin
// 性能敏感场景的日志记录
@Service
class HighPerformanceService {

    private val log = LoggerFactory.getLogger(HighPerformanceService::class.java)

    fun processLargeDataset(data: List<DataItem>) {
        // 使用isDebugEnabled避免不必要的字符串拼接
        if (log.isDebugEnabled) {
            log.debug("处理大数据集: size={}, sample={}",
                data.size, data.take(3))
        }

        // 批量处理时记录进度
        data.chunked(1000).forEachIndexed { index, chunk ->
            processChunk(chunk)

            if (index % 10 == 0) {  // 每处理10个批次记录一次
                log.info("数据处理进度: {}/{}",
                    (index + 1) * 1000, data.size)
            }
        }
    }
}
```
```
