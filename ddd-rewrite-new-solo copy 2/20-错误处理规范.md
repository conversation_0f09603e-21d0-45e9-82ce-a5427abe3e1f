# 超级个人全栈开发 - 错误处理规范文档

## 1. 错误处理总览

### 1.1 错误处理设计理念
- **用户友好**：为用户提供清晰、可理解的错误信息
- **开发友好**：为开发者提供详细的错误上下文和调试信息
- **统一标准**：前后端采用统一的错误处理标准和格式
- **可追踪性**：每个错误都有唯一标识，便于问题追踪和定位
- **安全性**：避免泄露敏感信息，保护系统安全
- **国际化**：支持多语言错误信息

### 1.2 错误处理架构图
```mermaid
graph TB
    subgraph "前端错误处理 (Frontend Error Handling)"
        VUE_ERROR[Vue全局错误处理<br/>app.config.errorHandler]
        AXIOS_INTERCEPTOR[Axios响应拦截器<br/>统一API错误处理]
        UI_ERROR[UI错误展示<br/>Toast/Modal/Notification]
        ERROR_BOUNDARY[错误边界<br/>组件级错误隔离]
    end
    
    subgraph "后端错误处理 (Backend Error Handling)"
        GLOBAL_HANDLER[全局异常处理器<br/>@ControllerAdvice]
        DOMAIN_EXCEPTION[领域异常<br/>业务规则违反]
        VALIDATION_ERROR[数据验证错误<br/>Bean Validation]
        SYSTEM_ERROR[系统异常<br/>技术错误]
    end
    
    subgraph "错误分类 (Error Classification)"
        BUSINESS_ERROR[业务错误<br/>4000-4999]
        VALIDATION_ERR[验证错误<br/>4100-4199]
        AUTH_ERROR[认证错误<br/>4010-4019]
        PERMISSION_ERROR[权限错误<br/>4030-4039]
        SYSTEM_ERR[系统错误<br/>5000-5999]
    end
    
    subgraph "错误日志 (Error Logging)"
        ERROR_LOG[错误日志记录<br/>结构化日志]
        TRACE_ID[链路追踪ID<br/>分布式追踪]
        ALERT[错误告警<br/>实时监控]
    end
    
    VUE_ERROR --> AXIOS_INTERCEPTOR
    AXIOS_INTERCEPTOR --> UI_ERROR
    
    GLOBAL_HANDLER --> BUSINESS_ERROR
    GLOBAL_HANDLER --> VALIDATION_ERR
    GLOBAL_HANDLER --> SYSTEM_ERR
    
    GLOBAL_HANDLER --> ERROR_LOG
    ERROR_LOG --> TRACE_ID
    ERROR_LOG --> ALERT
```

## 2. 后端错误处理实现

### 2.1 统一错误响应格式
```kotlin
// 统一错误响应类
@JsonInclude(JsonInclude.Include.NON_NULL)
data class ErrorResponse(
    val success: Boolean = false,
    val errorCode: String,
    val message: String,
    val details: String? = null,
    val timestamp: Instant = Instant.now(),
    val traceId: String? = null,
    val path: String? = null,
    val validationErrors: Map<String, String>? = null
) {
    companion object {
        fun businessError(code: String, message: String, details: String? = null): ErrorResponse {
            return ErrorResponse(
                errorCode = code,
                message = message,
                details = details,
                traceId = MDC.get("traceId")
            )
        }
        
        fun validationError(message: String, errors: Map<String, String>): ErrorResponse {
            return ErrorResponse(
                errorCode = "VALIDATION_ERROR",
                message = message,
                validationErrors = errors,
                traceId = MDC.get("traceId")
            )
        }
        
        fun systemError(message: String = "系统内部错误"): ErrorResponse {
            return ErrorResponse(
                errorCode = "SYSTEM_ERROR",
                message = message,
                traceId = MDC.get("traceId")
            )
        }
    }
}
```

### 2.2 错误码定义标准
```kotlin
// 错误码枚举
enum class ErrorCode(val code: String, val message: String) {
    // 业务错误 4000-4999
    REQUIREMENT_NOT_FOUND("REQ_4001", "采购需求不存在"),
    REQUIREMENT_ALREADY_PUBLISHED("REQ_4002", "采购需求已发布，无法修改"),
    REQUIREMENT_DEADLINE_PASSED("REQ_4003", "采购需求已过期"),
    
    SUPPLIER_NOT_QUALIFIED("SUP_4001", "供应商资质不符合要求"),
    SUPPLIER_BLACKLISTED("SUP_4002", "供应商已被列入黑名单"),
    
    BID_AMOUNT_INVALID("BID_4001", "投标金额无效"),
    BID_DEADLINE_PASSED("BID_4002", "投标截止时间已过"),
    
    ORDER_STATUS_INVALID("ORD_4001", "订单状态无效"),
    ORDER_CANNOT_CANCEL("ORD_4002", "订单无法取消"),
    
    INVENTORY_INSUFFICIENT("INV_4001", "库存不足"),
    INVENTORY_LOCKED("INV_4002", "库存已被锁定"),
    
    // 验证错误 4100-4199
    INVALID_EMAIL_FORMAT("VAL_4101", "邮箱格式无效"),
    INVALID_PHONE_FORMAT("VAL_4102", "手机号格式无效"),
    INVALID_AMOUNT_RANGE("VAL_4103", "金额范围无效"),
    REQUIRED_FIELD_MISSING("VAL_4104", "必填字段缺失"),
    
    // 认证错误 4010-4019
    INVALID_TOKEN("AUTH_4011", "无效的访问令牌"),
    TOKEN_EXPIRED("AUTH_4012", "访问令牌已过期"),
    INVALID_CREDENTIALS("AUTH_4013", "用户名或密码错误"),
    
    // 权限错误 4030-4039
    ACCESS_DENIED("PERM_4031", "访问被拒绝"),
    INSUFFICIENT_PRIVILEGES("PERM_4032", "权限不足"),
    RESOURCE_FORBIDDEN("PERM_4033", "资源访问被禁止"),
    
    // 系统错误 5000-5999
    DATABASE_ERROR("SYS_5001", "数据库操作失败"),
    EXTERNAL_SERVICE_ERROR("SYS_5002", "外部服务调用失败"),
    CONFIGURATION_ERROR("SYS_5003", "系统配置错误"),
    UNKNOWN_ERROR("SYS_5999", "未知系统错误");
    
    override fun toString(): String = code
}
```

### 2.3 领域异常定义
```kotlin
// 基础领域异常
abstract class DomainException(
    val errorCode: ErrorCode,
    override val message: String = errorCode.message,
    val details: String? = null,
    override val cause: Throwable? = null
) : RuntimeException(message, cause)

// 业务规则异常
class BusinessRuleViolationException(
    errorCode: ErrorCode,
    message: String = errorCode.message,
    details: String? = null
) : DomainException(errorCode, message, details)

// 资源未找到异常
class ResourceNotFoundException(
    errorCode: ErrorCode,
    message: String = errorCode.message,
    details: String? = null
) : DomainException(errorCode, message, details)

// 状态冲突异常
class StateConflictException(
    errorCode: ErrorCode,
    message: String = errorCode.message,
    details: String? = null
) : DomainException(errorCode, message, details)

// 权限异常
class PermissionDeniedException(
    errorCode: ErrorCode,
    message: String = errorCode.message,
    details: String? = null
) : DomainException(errorCode, message, details)
```

### 2.4 全局异常处理器
```kotlin
@ControllerAdvice
@Slf4j
class GlobalExceptionHandler {
    
    @ExceptionHandler(DomainException::class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    fun handleDomainException(
        ex: DomainException,
        request: HttpServletRequest
    ): ErrorResponse {
        log.warn("Domain exception: {}", ex.message, ex)
        return ErrorResponse.businessError(
            code = ex.errorCode.code,
            message = ex.message,
            details = ex.details
        ).copy(path = request.requestURI)
    }
    
    @ExceptionHandler(MethodArgumentNotValidException::class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    fun handleValidationException(
        ex: MethodArgumentNotValidException,
        request: HttpServletRequest
    ): ErrorResponse {
        val errors = ex.bindingResult.fieldErrors.associate { 
            it.field to (it.defaultMessage ?: "验证失败")
        }
        
        log.warn("Validation exception: {}", errors)
        return ErrorResponse.validationError(
            message = "数据验证失败",
            errors = errors
        ).copy(path = request.requestURI)
    }
    
    @ExceptionHandler(ConstraintViolationException::class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    fun handleConstraintViolationException(
        ex: ConstraintViolationException,
        request: HttpServletRequest
    ): ErrorResponse {
        val errors = ex.constraintViolations.associate {
            it.propertyPath.toString() to it.message
        }
        
        log.warn("Constraint violation: {}", errors)
        return ErrorResponse.validationError(
            message = "约束验证失败",
            errors = errors
        ).copy(path = request.requestURI)
    }
    
    @ExceptionHandler(AccessDeniedException::class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    fun handleAccessDeniedException(
        ex: AccessDeniedException,
        request: HttpServletRequest
    ): ErrorResponse {
        log.warn("Access denied: {}", ex.message)
        return ErrorResponse.businessError(
            code = ErrorCode.ACCESS_DENIED.code,
            message = "访问被拒绝"
        ).copy(path = request.requestURI)
    }
    
    @ExceptionHandler(Exception::class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    fun handleGenericException(
        ex: Exception,
        request: HttpServletRequest
    ): ErrorResponse {
        log.error("Unexpected exception: {}", ex.message, ex)
        return ErrorResponse.systemError(
            message = "系统内部错误，请稍后重试"
        ).copy(path = request.requestURI)
    }
}
```

## 3. 前端错误处理实现

### 3.1 Axios错误拦截器
```typescript
// axios错误拦截器配置
import axios, { AxiosError, AxiosResponse } from 'axios'
import { ElMessage, ElNotification } from 'element-plus'

// 错误响应接口
interface ErrorResponse {
  success: boolean
  errorCode: string
  message: string
  details?: string
  timestamp: string
  traceId?: string
  path?: string
  validationErrors?: Record<string, string>
}

// 响应拦截器
axios.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error: AxiosError<ErrorResponse>) => {
    const { response } = error
    
    if (response) {
      const { status, data } = response
      
      switch (status) {
        case 400:
          handleBadRequest(data)
          break
        case 401:
          handleUnauthorized(data)
          break
        case 403:
          handleForbidden(data)
          break
        case 404:
          handleNotFound(data)
          break
        case 500:
          handleServerError(data)
          break
        default:
          handleUnknownError(data)
      }
    } else {
      handleNetworkError()
    }
    
    return Promise.reject(error)
  }
)

// 错误处理函数
function handleBadRequest(data: ErrorResponse) {
  if (data.validationErrors) {
    // 显示验证错误
    Object.entries(data.validationErrors).forEach(([field, message]) => {
      ElMessage.error(`${field}: ${message}`)
    })
  } else {
    ElMessage.error(data.message || '请求参数错误')
  }
}

function handleUnauthorized(data: ErrorResponse) {
  ElMessage.error('登录已过期，请重新登录')
  // 跳转到登录页
  router.push('/login')
}

function handleForbidden(data: ErrorResponse) {
  ElMessage.error(data.message || '权限不足')
}

function handleNotFound(data: ErrorResponse) {
  ElMessage.error(data.message || '请求的资源不存在')
}

function handleServerError(data: ErrorResponse) {
  ElNotification.error({
    title: '系统错误',
    message: data.message || '服务器内部错误，请稍后重试',
    duration: 5000
  })
}

function handleNetworkError() {
  ElNotification.error({
    title: '网络错误',
    message: '网络连接失败，请检查网络设置',
    duration: 5000
  })
}

function handleUnknownError(data: ErrorResponse) {
  ElMessage.error(data?.message || '未知错误')
}
```

### 3.2 Vue全局错误处理
```typescript
// main.ts - Vue全局错误处理配置
import { createApp } from 'vue'
import App from './App.vue'

const app = createApp(App)

// 全局错误处理器
app.config.errorHandler = (err: unknown, instance, info) => {
  console.error('Vue全局错误:', err)
  console.error('错误信息:', info)

  // 发送错误到监控系统
  reportError(err as Error, {
    component: instance?.$options.name || 'Unknown',
    errorInfo: info
  })

  // 显示用户友好的错误信息
  ElNotification.error({
    title: '应用错误',
    message: '应用出现异常，请刷新页面重试',
    duration: 5000
  })
}

// 错误上报函数
function reportError(error: Error, context: any) {
  // 这里可以集成Sentry或其他错误监控服务
  console.error('Error reported:', {
    message: error.message,
    stack: error.stack,
    context
  })
}
```

### 3.3 组件级错误处理
```vue
<!-- ErrorBoundary.vue - 错误边界组件 -->
<template>
  <div v-if="hasError" class="error-boundary">
    <el-alert
      title="组件加载失败"
      type="error"
      :description="errorMessage"
      show-icon
      :closable="false"
    />
    <el-button @click="retry" type="primary" style="margin-top: 16px">
      重试
    </el-button>
  </div>
  <slot v-else />
</template>

<script setup lang="ts">
import { ref, onErrorCaptured } from 'vue'
import { ElAlert, ElButton, ElMessage } from 'element-plus'

const hasError = ref(false)
const errorMessage = ref('')

// 捕获子组件错误
onErrorCaptured((err: Error, instance, info) => {
  console.error('组件错误:', err)
  hasError.value = true
  errorMessage.value = err.message || '组件渲染失败'

  // 阻止错误继续向上传播
  return false
})

// 重试函数
const retry = () => {
  hasError.value = false
  errorMessage.value = ''
  ElMessage.success('正在重新加载...')
}
</script>
```

## 4. 错误处理最佳实践

### 4.1 领域层错误处理
```kotlin
// 在聚合根中抛出业务异常
class ProcurementRequirement {
    fun publish(): RequirementPublishedEvent {
        if (status != RequirementStatus.DRAFT) {
            throw BusinessRuleViolationException(
                ErrorCode.REQUIREMENT_ALREADY_PUBLISHED,
                details = "当前状态: $status"
            )
        }

        if (deadline.isBefore(LocalDate.now())) {
            throw BusinessRuleViolationException(
                ErrorCode.REQUIREMENT_DEADLINE_PASSED,
                details = "截止日期: $deadline"
            )
        }

        status = RequirementStatus.PUBLISHED
        return RequirementPublishedEvent(id.value, buyerId.value)
    }
}
```

### 4.2 应用服务层错误处理
```kotlin
@Service
@Transactional
class RequirementApplicationService {

    fun createRequirement(command: CreateRequirementCommand): RequirementId {
        return try {
            val requirement = ProcurementRequirement.create(command)
            requirementRepository.save(requirement)
            requirement.id
        } catch (DomainException ex) {
            // 领域异常直接抛出，由全局异常处理器处理
            throw ex
        } catch (Exception ex) {
            // 包装系统异常
            log.error("创建采购需求失败", ex)
            throw BusinessRuleViolationException(
                ErrorCode.UNKNOWN_ERROR,
                "创建采购需求失败",
                ex.message
            )
        }
    }
}
```

### 4.3 前端组件错误处理
```vue
<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAsyncState } from '@vueuse/core'

// 使用useAsyncState处理异步操作错误
const { state: requirements, isLoading, error, execute } = useAsyncState(
  () => requirementApi.getRequirements(),
  [],
  {
    onError: (err) => {
      console.error('获取需求列表失败:', err)
      ElMessage.error('获取需求列表失败，请稍后重试')
    }
  }
)

// 错误状态计算属性
const hasError = computed(() => !!error.value)
const errorMessage = computed(() => {
  if (!error.value) return ''
  return error.value.response?.data?.message || '加载失败'
})

// 重试函数
const retry = () => {
  execute()
}
</script>

<template>
  <div>
    <!-- 加载状态 -->
    <el-loading v-if="isLoading" />

    <!-- 错误状态 -->
    <div v-else-if="hasError" class="error-state">
      <el-empty description="加载失败">
        <el-button @click="retry" type="primary">重试</el-button>
      </el-empty>
    </div>

    <!-- 正常内容 -->
    <div v-else>
      <!-- 需求列表内容 -->
    </div>
  </div>
</template>
```

## 5. 错误监控和告警

### 5.1 错误日志记录
```kotlin
// 错误日志记录配置
@Component
class ErrorLogger {

    private val logger = LoggerFactory.getLogger(ErrorLogger::class.java)

    @EventListener
    fun handleDomainException(ex: DomainException) {
        val errorLog = mapOf(
            "errorCode" to ex.errorCode.code,
            "message" to ex.message,
            "details" to ex.details,
            "traceId" to MDC.get("traceId"),
            "userId" to SecurityContextHolder.getContext().authentication?.name,
            "timestamp" to Instant.now().toString()
        )

        logger.warn("Domain exception occurred: {}", errorLog)
    }

    @EventListener
    fun handleSystemException(ex: Exception) {
        val errorLog = mapOf(
            "errorType" to ex.javaClass.simpleName,
            "message" to ex.message,
            "stackTrace" to ex.stackTraceToString(),
            "traceId" to MDC.get("traceId"),
            "timestamp" to Instant.now().toString()
        )

        logger.error("System exception occurred: {}", errorLog)
    }
}
```

### 5.2 错误统计和分析
```kotlin
// 错误统计服务
@Service
class ErrorStatisticsService {

    private val errorCounter = Counter.builder("application_errors_total")
        .description("Total number of application errors")
        .tag("type", "unknown")
        .register(Metrics.globalRegistry)

    fun recordError(errorCode: String, errorType: String) {
        errorCounter.increment(
            Tags.of(
                "error_code", errorCode,
                "error_type", errorType
            )
        )
    }
}
```

## 6. 错误处理测试

### 6.1 单元测试
```kotlin
@Test
fun `should throw exception when requirement already published`() {
    // Given
    val requirement = ProcurementRequirement.create(validCommand)
    requirement.publish() // 先发布

    // When & Then
    val exception = assertThrows<BusinessRuleViolationException> {
        requirement.publish() // 再次发布应该抛出异常
    }

    assertThat(exception.errorCode).isEqualTo(ErrorCode.REQUIREMENT_ALREADY_PUBLISHED)
    assertThat(exception.message).contains("已发布")
}
```

### 6.2 集成测试
```kotlin
@Test
fun `should return error response when validation fails`() {
    // Given
    val invalidRequest = CreateRequirementRequest(
        title = "", // 无效的标题
        description = "测试描述"
    )

    // When
    val response = mockMvc.perform(
        post("/api/requirements")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(invalidRequest))
    )

    // Then
    response.andExpect(status().isBadRequest)
        .andExpect(jsonPath("$.success").value(false))
        .andExpect(jsonPath("$.errorCode").value("VALIDATION_ERROR"))
        .andExpect(jsonPath("$.validationErrors.title").exists())
}
```
```
