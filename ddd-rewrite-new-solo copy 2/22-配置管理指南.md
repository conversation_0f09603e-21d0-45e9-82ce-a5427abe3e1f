# 超级个人全栈开发 - 配置管理指南文档

## 1. 配置管理总览

### 1.1 配置管理设计理念
- **环境分离**：不同环境使用不同的配置文件，确保环境隔离
- **敏感信息保护**：敏感配置加密存储，避免明文泄露
- **配置热更新**：支持运行时配置更新，无需重启应用
- **版本管理**：配置文件纳入版本控制，可追踪变更历史
- **统一管理**：前后端配置统一管理，保持一致性
- **默认值策略**：合理设置默认值，降低配置复杂度

### 1.2 配置管理架构图
```mermaid
graph TB
    subgraph "配置源 (Configuration Sources)"
        FILES[配置文件<br/>application.yml]
        ENV[环境变量<br/>Environment Variables]
        ARGS[命令行参数<br/>Command Line Args]
        VAULT[配置中心<br/>Spring Cloud Config]
    end
    
    subgraph "配置加载 (Configuration Loading)"
        SPRING_CONFIG[Spring Boot配置<br/>@ConfigurationProperties]
        PROFILES[环境配置<br/>Spring Profiles]
        ENCRYPTION[配置加密<br/>Jasypt]
        VALIDATION[配置验证<br/>Bean Validation]
    end
    
    subgraph "配置分类 (Configuration Categories)"
        APP_CONFIG[应用配置<br/>端口、路径等]
        DB_CONFIG[数据库配置<br/>连接信息]
        CACHE_CONFIG[缓存配置<br/>Redis设置]
        SECURITY_CONFIG[安全配置<br/>JWT、OAuth2]
        BUSINESS_CONFIG[业务配置<br/>业务规则参数]
        INTEGRATION_CONFIG[集成配置<br/>第三方服务]
    end
    
    subgraph "配置管理 (Configuration Management)"
        HOT_RELOAD[热更新<br/>@RefreshScope]
        CONFIG_MONITOR[配置监控<br/>配置变更通知]
        CONFIG_AUDIT[配置审计<br/>变更记录]
        CONFIG_BACKUP[配置备份<br/>历史版本]
    end
    
    FILES --> SPRING_CONFIG
    ENV --> SPRING_CONFIG
    ARGS --> SPRING_CONFIG
    VAULT --> SPRING_CONFIG
    
    SPRING_CONFIG --> PROFILES
    SPRING_CONFIG --> ENCRYPTION
    SPRING_CONFIG --> VALIDATION
    
    PROFILES --> APP_CONFIG
    PROFILES --> DB_CONFIG
    PROFILES --> CACHE_CONFIG
    PROFILES --> SECURITY_CONFIG
    
    SPRING_CONFIG --> HOT_RELOAD
    HOT_RELOAD --> CONFIG_MONITOR
    CONFIG_MONITOR --> CONFIG_AUDIT
    CONFIG_AUDIT --> CONFIG_BACKUP
```

## 2. 后端配置管理

### 2.1 Spring Boot配置文件结构
```yaml
# application.yml - 基础配置
spring:
  application:
    name: procurement-platform
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  
  # 数据源配置
  datasource:
    url: ${DATABASE_URL:********************************/procurement_platform}
    username: ${DATABASE_USERNAME:procurement_user}
    password: ${DATABASE_PASSWORD:procurement_pass}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: ${DB_POOL_SIZE:20}
      minimum-idle: ${DB_POOL_MIN_IDLE:5}
      connection-timeout: ${DB_CONNECTION_TIMEOUT:30000}
      idle-timeout: ${DB_IDLE_TIMEOUT:600000}
      max-lifetime: ${DB_MAX_LIFETIME:1800000}
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: ${JPA_DDL_AUTO:validate}
    show-sql: ${JPA_SHOW_SQL:false}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DATABASE:0}
    timeout: ${REDIS_TIMEOUT:2000ms}
    lettuce:
      pool:
        max-active: ${REDIS_POOL_MAX_ACTIVE:8}
        max-idle: ${REDIS_POOL_MAX_IDLE:8}
        min-idle: ${REDIS_POOL_MIN_IDLE:0}
        max-wait: ${REDIS_POOL_MAX_WAIT:-1ms}
  
  # 安全配置
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${JWT_ISSUER_URI:http://localhost:8080}
          jwk-set-uri: ${JWT_JWK_SET_URI:http://localhost:8080/.well-known/jwks.json}

# 服务器配置
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: ${SERVER_CONTEXT_PATH:/api}
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
  http2:
    enabled: true

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: ${MANAGEMENT_ENDPOINTS:health,info,metrics,prometheus}
  endpoint:
    health:
      show-details: ${MANAGEMENT_HEALTH_DETAILS:when-authorized}
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    com.procurement.platform: ${LOG_LEVEL_APP:INFO}
    org.springframework.security: ${LOG_LEVEL_SECURITY:WARN}
    org.hibernate.SQL: ${LOG_LEVEL_SQL:WARN}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-}] %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE_NAME:logs/application.log}

# 应用自定义配置
app:
  # JWT配置
  jwt:
    secret: ${JWT_SECRET:ENC(encrypted_jwt_secret)}
    expiration: ${JWT_EXPIRATION:86400} # 24小时
    refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800} # 7天
  
  # 文件上传配置
  upload:
    max-file-size: ${UPLOAD_MAX_FILE_SIZE:10MB}
    max-request-size: ${UPLOAD_MAX_REQUEST_SIZE:50MB}
    temp-dir: ${UPLOAD_TEMP_DIR:/tmp/uploads}
    allowed-types: ${UPLOAD_ALLOWED_TYPES:jpg,jpeg,png,pdf,doc,docx,xls,xlsx}
  
  # 业务配置
  business:
    requirement:
      max-items: ${REQUIREMENT_MAX_ITEMS:100}
      default-deadline-days: ${REQUIREMENT_DEFAULT_DEADLINE_DAYS:30}
    bidding:
      min-duration-hours: ${BIDDING_MIN_DURATION_HOURS:24}
      max-duration-days: ${BIDDING_MAX_DURATION_DAYS:30}
    order:
      auto-confirm-hours: ${ORDER_AUTO_CONFIRM_HOURS:72}
      payment-timeout-hours: ${ORDER_PAYMENT_TIMEOUT_HOURS:24}
  
  # 第三方服务配置
  external:
    payment:
      alipay:
        app-id: ${ALIPAY_APP_ID:}
        private-key: ${ALIPAY_PRIVATE_KEY:ENC(encrypted_alipay_private_key)}
        public-key: ${ALIPAY_PUBLIC_KEY:ENC(encrypted_alipay_public_key)}
        gateway-url: ${ALIPAY_GATEWAY_URL:https://openapi.alipay.com/gateway.do}
      wechat:
        app-id: ${WECHAT_APP_ID:}
        mch-id: ${WECHAT_MCH_ID:}
        api-key: ${WECHAT_API_KEY:ENC(encrypted_wechat_api_key)}
    sms:
      provider: ${SMS_PROVIDER:aliyun}
      aliyun:
        access-key-id: ${ALIYUN_SMS_ACCESS_KEY_ID:}
        access-key-secret: ${ALIYUN_SMS_ACCESS_KEY_SECRET:ENC(encrypted_aliyun_sms_secret)}
        sign-name: ${ALIYUN_SMS_SIGN_NAME:采购平台}
    email:
      smtp:
        host: ${EMAIL_SMTP_HOST:smtp.qq.com}
        port: ${EMAIL_SMTP_PORT:587}
        username: ${EMAIL_SMTP_USERNAME:}
        password: ${EMAIL_SMTP_PASSWORD:ENC(encrypted_email_password)}
        auth: true
        starttls: true
```

### 2.2 环境特定配置
```yaml
# application-dev.yml - 开发环境配置
spring:
  datasource:
    url: ********************************/procurement_platform_dev
    username: dev_user
    password: dev_pass
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
  
  redis:
    host: localhost
    port: 6379
    password: ""

logging:
  level:
    com.procurement.platform: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

app:
  jwt:
    secret: dev_jwt_secret_key_for_development_only
  external:
    payment:
      alipay:
        gateway-url: https://openapi.alipaydev.com/gateway.do # 沙箱环境

---
# application-test.yml - 测试环境配置
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: ""
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
  
  redis:
    host: localhost
    port: 6379

app:
  jwt:
    secret: test_jwt_secret_key_for_testing_only
    expiration: 3600 # 1小时，测试环境短一些

---
# application-prod.yml - 生产环境配置
spring:
  datasource:
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

logging:
  level:
    com.procurement.platform: INFO
    org.springframework: WARN
    org.hibernate: WARN
  file:
    name: /var/log/procurement-platform/application.log

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: never

app:
  business:
    requirement:
      max-items: 200 # 生产环境允许更多项目
    order:
      auto-confirm-hours: 48 # 生产环境更短的确认时间
```

### 2.3 配置属性类
```kotlin
// 应用配置属性
@ConfigurationProperties(prefix = "app")
@ConstructorBinding
@Validated
data class AppProperties(
    val jwt: JwtProperties,
    val upload: UploadProperties,
    val business: BusinessProperties,
    val external: ExternalProperties
)

@Validated
data class JwtProperties(
    @field:NotBlank
    val secret: String,
    
    @field:Min(3600)
    val expiration: Long = 86400,
    
    @field:Min(86400)
    val refreshExpiration: Long = 604800
)

@Validated
data class UploadProperties(
    @field:NotNull
    val maxFileSize: DataSize = DataSize.ofMegabytes(10),
    
    @field:NotNull
    val maxRequestSize: DataSize = DataSize.ofMegabytes(50),
    
    @field:NotBlank
    val tempDir: String = "/tmp/uploads",
    
    val allowedTypes: Set<String> = setOf("jpg", "jpeg", "png", "pdf", "doc", "docx")
)

@Validated
data class BusinessProperties(
    val requirement: RequirementProperties = RequirementProperties(),
    val bidding: BiddingProperties = BiddingProperties(),
    val order: OrderProperties = OrderProperties()
)

data class RequirementProperties(
    @field:Min(1)
    val maxItems: Int = 100,
    
    @field:Min(1)
    val defaultDeadlineDays: Int = 30
)

data class BiddingProperties(
    @field:Min(1)
    val minDurationHours: Int = 24,
    
    @field:Min(1)
    val maxDurationDays: Int = 30
)

data class OrderProperties(
    @field:Min(1)
    val autoConfirmHours: Int = 72,
    
    @field:Min(1)
    val paymentTimeoutHours: Int = 24
)

data class ExternalProperties(
    val payment: PaymentProperties = PaymentProperties(),
    val sms: SmsProperties = SmsProperties(),
    val email: EmailProperties = EmailProperties()
)

// 配置属性启用
@Configuration
@EnableConfigurationProperties(AppProperties::class)
class ConfigurationPropertiesConfig
```

### 2.4 配置加密
```kotlin
// Jasypt配置加密
@Configuration
class JasyptConfig {
    
    @Bean("jasyptStringEncryptor")
    fun stringEncryptor(): StringEncryptor {
        val encryptor = PooledPBEStringEncryptor()
        val config = SimpleStringPBEConfig()
        
        config.password = System.getenv("JASYPT_ENCRYPTOR_PASSWORD") 
            ?: "default_encryption_password"
        config.algorithm = "PBEWITHHMACSHA512ANDAES_256"
        config.keyObtentionIterations = "1000"
        config.poolSize = "1"
        config.providerName = "SunJCE"
        config.saltGeneratorClassName = "org.jasypt.salt.RandomSaltGenerator"
        config.ivGeneratorClassName = "org.jasypt.iv.RandomIvGenerator"
        config.stringOutputType = "base64"
        
        encryptor.setConfig(config)
        return encryptor
    }
}

// 配置加密工具
@Component
class ConfigEncryptionUtil {
    
    @Autowired
    private lateinit var stringEncryptor: StringEncryptor
    
    fun encrypt(plainText: String): String {
        return "ENC(${stringEncryptor.encrypt(plainText)})"
    }
    
    fun decrypt(encryptedText: String): String {
        val encrypted = encryptedText.removePrefix("ENC(").removeSuffix(")")
        return stringEncryptor.decrypt(encrypted)
    }
}
```

## 3. 前端配置管理

### 3.1 环境配置文件
```typescript
// .env - 基础环境变量
VITE_APP_TITLE=采购生态平台
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=企业级采购生态平台

# API配置
VITE_API_BASE_URL=/api
VITE_API_TIMEOUT=30000

# 上传配置
VITE_UPLOAD_MAX_SIZE=10485760
VITE_UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx

# 分页配置
VITE_PAGE_SIZE_DEFAULT=20
VITE_PAGE_SIZE_OPTIONS=10,20,50,100

# 缓存配置
VITE_CACHE_EXPIRE_TIME=3600000
VITE_TOKEN_STORAGE_KEY=procurement_token
```

```typescript
// .env.development - 开发环境
NODE_ENV=development
VITE_APP_ENV=development

# API配置
VITE_API_BASE_URL=http://localhost:8080/api
VITE_API_MOCK=false

# 调试配置
VITE_DEBUG=true
VITE_LOG_LEVEL=debug

# 开发工具
VITE_DEVTOOLS=true
VITE_SOURCE_MAP=true
```

```typescript
// .env.production - 生产环境
NODE_ENV=production
VITE_APP_ENV=production

# API配置
VITE_API_BASE_URL=https://api.procurement-platform.com/api
VITE_API_MOCK=false

# 性能配置
VITE_DEBUG=false
VITE_LOG_LEVEL=error
VITE_DEVTOOLS=false
VITE_SOURCE_MAP=false

# CDN配置
VITE_CDN_URL=https://cdn.procurement-platform.com
```

### 3.2 配置管理类
```typescript
// config/index.ts - 配置管理
interface AppConfig {
  app: {
    title: string
    version: string
    description: string
    env: string
  }
  api: {
    baseURL: string
    timeout: number
    mock: boolean
  }
  upload: {
    maxSize: number
    allowedTypes: string[]
  }
  pagination: {
    defaultPageSize: number
    pageSizeOptions: number[]
  }
  cache: {
    expireTime: number
    tokenStorageKey: string
  }
  debug: {
    enabled: boolean
    logLevel: string
    devtools: boolean
    sourceMap: boolean
  }
  cdn: {
    url: string
  }
}

class ConfigManager {
  private config: AppConfig
  
  constructor() {
    this.config = this.loadConfig()
  }
  
  private loadConfig(): AppConfig {
    return {
      app: {
        title: import.meta.env.VITE_APP_TITLE || '采购生态平台',
        version: import.meta.env.VITE_APP_VERSION || '1.0.0',
        description: import.meta.env.VITE_APP_DESCRIPTION || '',
        env: import.meta.env.VITE_APP_ENV || 'development'
      },
      api: {
        baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
        timeout: Number(import.meta.env.VITE_API_TIMEOUT) || 30000,
        mock: import.meta.env.VITE_API_MOCK === 'true'
      },
      upload: {
        maxSize: Number(import.meta.env.VITE_UPLOAD_MAX_SIZE) || 10485760,
        allowedTypes: (import.meta.env.VITE_UPLOAD_ALLOWED_TYPES || '').split(',')
      },
      pagination: {
        defaultPageSize: Number(import.meta.env.VITE_PAGE_SIZE_DEFAULT) || 20,
        pageSizeOptions: (import.meta.env.VITE_PAGE_SIZE_OPTIONS || '10,20,50,100')
          .split(',').map(Number)
      },
      cache: {
        expireTime: Number(import.meta.env.VITE_CACHE_EXPIRE_TIME) || 3600000,
        tokenStorageKey: import.meta.env.VITE_TOKEN_STORAGE_KEY || 'procurement_token'
      },
      debug: {
        enabled: import.meta.env.VITE_DEBUG === 'true',
        logLevel: import.meta.env.VITE_LOG_LEVEL || 'info',
        devtools: import.meta.env.VITE_DEVTOOLS === 'true',
        sourceMap: import.meta.env.VITE_SOURCE_MAP === 'true'
      },
      cdn: {
        url: import.meta.env.VITE_CDN_URL || ''
      }
    }
  }
  
  get<K extends keyof AppConfig>(key: K): AppConfig[K] {
    return this.config[key]
  }
  
  getAll(): AppConfig {
    return { ...this.config }
  }
  
  isDevelopment(): boolean {
    return this.config.app.env === 'development'
  }
  
  isProduction(): boolean {
    return this.config.app.env === 'production'
  }
  
  // 运行时配置更新
  updateConfig(updates: Partial<AppConfig>): void {
    this.config = { ...this.config, ...updates }
  }
}

export const config = new ConfigManager()
export default config
```

### 3.3 配置验证
```typescript
// config/validator.ts - 配置验证
import { z } from 'zod'

const AppConfigSchema = z.object({
  app: z.object({
    title: z.string().min(1),
    version: z.string().regex(/^\d+\.\d+\.\d+$/),
    description: z.string(),
    env: z.enum(['development', 'test', 'production'])
  }),
  api: z.object({
    baseURL: z.string().url(),
    timeout: z.number().min(1000).max(60000),
    mock: z.boolean()
  }),
  upload: z.object({
    maxSize: z.number().min(1024).max(104857600), // 1KB - 100MB
    allowedTypes: z.array(z.string()).min(1)
  }),
  pagination: z.object({
    defaultPageSize: z.number().min(10).max(100),
    pageSizeOptions: z.array(z.number()).min(1)
  })
})

export function validateConfig(config: unknown): AppConfig {
  try {
    return AppConfigSchema.parse(config)
  } catch (error) {
    console.error('配置验证失败:', error)
    throw new Error('应用配置无效')
  }
}
```

## 4. 配置热更新

### 4.1 后端配置热更新
```kotlin
// 配置热更新支持
@RefreshScope
@RestController
@RequestMapping("/api/admin/config")
class ConfigController {

    @Autowired
    private lateinit var appProperties: AppProperties

    @Autowired
    private lateinit var refreshEndpoint: RefreshEndpoint

    @PostMapping("/refresh")
    @PreAuthorize("hasRole('ADMIN')")
    fun refreshConfig(): ResponseEntity<Map<String, Any>> {
        val changedKeys = refreshEndpoint.refresh()

        return ResponseEntity.ok(mapOf(
            "success" to true,
            "message" to "配置刷新成功",
            "changedKeys" to changedKeys,
            "timestamp" to Instant.now()
        ))
    }

    @GetMapping("/current")
    @PreAuthorize("hasRole('ADMIN')")
    fun getCurrentConfig(): ResponseEntity<AppProperties> {
        return ResponseEntity.ok(appProperties)
    }
}

// 配置变更监听器
@Component
class ConfigChangeListener {

    private val log = LoggerFactory.getLogger(ConfigChangeListener::class.java)

    @EventListener
    fun handleRefreshEvent(event: RefreshEvent) {
        log.info("配置刷新事件: changedKeys={}", event.changedKeys)

        // 发送配置变更通知
        notifyConfigChange(event.changedKeys)
    }

    private fun notifyConfigChange(changedKeys: Set<String>) {
        // 这里可以发送邮件、短信或其他通知
        log.info("配置变更通知已发送: {}", changedKeys)
    }
}
```

### 4.2 前端配置热更新
```typescript
// config/hot-reload.ts - 前端配置热更新
class ConfigHotReload {
  private config: ConfigManager
  private listeners: Array<(config: AppConfig) => void> = []
  private pollingInterval: number = 30000 // 30秒
  private timer?: number

  constructor(config: ConfigManager) {
    this.config = config
  }

  start(): void {
    this.timer = window.setInterval(() => {
      this.checkConfigUpdate()
    }, this.pollingInterval)
  }

  stop(): void {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = undefined
    }
  }

  onConfigChange(listener: (config: AppConfig) => void): void {
    this.listeners.push(listener)
  }

  private async checkConfigUpdate(): Promise<void> {
    try {
      const response = await fetch('/api/config/current')
      const serverConfig = await response.json()

      // 比较配置是否有变化
      if (this.hasConfigChanged(serverConfig)) {
        this.config.updateConfig(serverConfig)
        this.notifyListeners(serverConfig)
      }
    } catch (error) {
      console.error('检查配置更新失败:', error)
    }
  }

  private hasConfigChanged(newConfig: AppConfig): boolean {
    const currentConfig = this.config.getAll()
    return JSON.stringify(currentConfig) !== JSON.stringify(newConfig)
  }

  private notifyListeners(config: AppConfig): void {
    this.listeners.forEach(listener => {
      try {
        listener(config)
      } catch (error) {
        console.error('配置变更监听器执行失败:', error)
      }
    })
  }
}

// 使用配置热更新
const hotReload = new ConfigHotReload(config)

// 监听配置变更
hotReload.onConfigChange((newConfig) => {
  console.log('配置已更新:', newConfig)

  // 更新axios配置
  axios.defaults.baseURL = newConfig.api.baseURL
  axios.defaults.timeout = newConfig.api.timeout

  // 更新其他组件配置
  ElMessage.success('配置已更新')
})

// 启动热更新
if (config.isDevelopment()) {
  hotReload.start()
}
```

## 5. 配置安全

### 5.1 敏感配置保护
```bash
# 生成加密密钥
export JASYPT_ENCRYPTOR_PASSWORD="your_secret_encryption_password"

# 加密敏感配置
java -cp jasypt-1.9.3.jar org.jasypt.intf.cli.JasyptPBEStringEncryptionCLI \
  input="your_secret_value" \
  password="your_secret_encryption_password" \
  algorithm="PBEWITHHMACSHA512ANDAES_256"
```

```kotlin
// 配置加密命令行工具
@Component
@Profile("!prod")
class ConfigEncryptionCLI : CommandLineRunner {

    @Autowired
    private lateinit var configEncryptionUtil: ConfigEncryptionUtil

    override fun run(vararg args: String?) {
        if (args.contains("--encrypt-config")) {
            val scanner = Scanner(System.`in`)

            println("配置加密工具")
            println("输入要加密的值 (输入 'exit' 退出):")

            while (true) {
                print("> ")
                val input = scanner.nextLine()

                if (input == "exit") break

                try {
                    val encrypted = configEncryptionUtil.encrypt(input)
                    println("加密结果: $encrypted")
                } catch (e: Exception) {
                    println("加密失败: ${e.message}")
                }
            }
        }
    }
}
```

### 5.2 配置访问控制
```kotlin
// 配置访问权限控制
@RestController
@RequestMapping("/api/admin/config")
@PreAuthorize("hasRole('ADMIN')")
class SecureConfigController {

    @GetMapping("/sensitive")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    fun getSensitiveConfig(): ResponseEntity<Map<String, String>> {
        // 只返回非敏感配置信息
        val safeConfig = mapOf(
            "database.url" to "********************************/***",
            "redis.host" to "localhost",
            "jwt.expiration" to "86400"
        )

        return ResponseEntity.ok(safeConfig)
    }

    @PostMapping("/update")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    fun updateConfig(
        @RequestBody @Valid request: ConfigUpdateRequest
    ): ResponseEntity<ApiResponse<String>> {

        // 记录配置变更审计日志
        auditLogger.info("配置更新请求: user={}, changes={}",
            SecurityContextHolder.getContext().authentication.name,
            request.changes)

        // 验证配置变更
        validateConfigChanges(request.changes)

        // 应用配置变更
        applyConfigChanges(request.changes)

        return ResponseEntity.ok(ApiResponse.success("配置更新成功"))
    }
}
```

## 6. 配置最佳实践

### 6.1 配置命名规范
```yaml
# 好的配置命名示例
app:
  # 使用层次结构
  database:
    connection:
      pool-size: 20
      timeout: 30000

  # 使用描述性名称
  business-rules:
    requirement:
      max-items-per-request: 100
      default-deadline-days: 30

  # 使用一致的命名风格
  external-services:
    payment-gateway:
      alipay:
        app-id: "your_app_id"
        gateway-url: "https://openapi.alipay.com/gateway.do"
```

### 6.2 配置文档化
```kotlin
// 配置属性文档化
@ConfigurationProperties(prefix = "app.business")
@Validated
data class BusinessProperties(
    /**
     * 采购需求配置
     */
    val requirement: RequirementProperties = RequirementProperties(),

    /**
     * 竞价配置
     */
    val bidding: BiddingProperties = BiddingProperties()
) {

    data class RequirementProperties(
        /**
         * 单个采购需求允许的最大项目数量
         * 默认值: 100
         * 范围: 1-1000
         */
        @field:Min(1)
        @field:Max(1000)
        val maxItems: Int = 100,

        /**
         * 采购需求默认截止天数
         * 默认值: 30天
         * 范围: 1-365天
         */
        @field:Min(1)
        @field:Max(365)
        val defaultDeadlineDays: Int = 30
    )
}
```

### 6.3 配置测试
```kotlin
// 配置属性测试
@SpringBootTest
@TestPropertySource(properties = [
    "app.business.requirement.max-items=50",
    "app.business.requirement.default-deadline-days=15"
])
class BusinessPropertiesTest {

    @Autowired
    private lateinit var businessProperties: BusinessProperties

    @Test
    fun `should load business properties correctly`() {
        assertThat(businessProperties.requirement.maxItems).isEqualTo(50)
        assertThat(businessProperties.requirement.defaultDeadlineDays).isEqualTo(15)
    }

    @Test
    fun `should validate business properties`() {
        val validator = Validation.buildDefaultValidatorFactory().validator

        val invalidProperties = BusinessProperties.RequirementProperties(
            maxItems = 0, // 无效值
            defaultDeadlineDays = 400 // 无效值
        )

        val violations = validator.validate(invalidProperties)
        assertThat(violations).hasSize(2)
    }
}
```
