# 超级个人全栈开发 - TDD测试策略文档

## 1. TDD测试策略总览

### 1.1 TDD驱动的测试理念
- **测试先行**：TDD三步法（红-绿-重构）贯穿整个开发过程
- **质量内建**：通过测试驱动设计，从源头保证代码质量
- **快速反馈**：秒级测试反馈，立即发现设计和实现问题
- **重构安全**：完善的测试覆盖为重构提供安全保障
- **文档化设计**：测试即文档，清晰表达设计意图
- **持续改进**：基于测试结果持续优化代码和架构

### 1.2 TDD开发循环
```mermaid
graph LR
    A[🔴 RED<br/>编写失败测试] --> B[🟢 GREEN<br/>编写最少代码]
    B --> C[🔵 REFACTOR<br/>重构优化]
    C --> A

    subgraph "TDD核心原则"
        D[测试先行<br/>Test First]
        E[小步快跑<br/>Baby Steps]
        F[持续重构<br/>Continuous Refactoring]
    end

    A -.-> D
    B -.-> E
    C -.-> F
```

### 1.3 TDD测试金字塔架构
```mermaid
graph TB
    subgraph "TDD测试金字塔 (TDD Test Pyramid)"
        E2E[端到端测试<br/>E2E Tests<br/>5%<br/>🎯 用户验收]
        INTEGRATION[集成测试<br/>Integration Tests<br/>25%<br/>🔗 组件协作]
        UNIT[单元测试<br/>Unit Tests<br/>70%<br/>⚡ TDD核心]
    end

    subgraph "TDD测试层级 (TDD Test Levels)"
        UNIT_DOMAIN[领域模型测试<br/>Domain Model Tests<br/>TDD驱动设计]
        UNIT_SERVICE[应用服务测试<br/>Application Service Tests<br/>行为验证]
        UNIT_COMPONENT[组件单元测试<br/>Component Unit Tests<br/>UI逻辑验证]

        INT_API[API集成测试<br/>API Integration Tests<br/>契约验证]
        INT_DB[数据库集成测试<br/>Database Integration Tests<br/>持久化验证]
        INT_CONTEXT[上下文集成测试<br/>Context Integration Tests<br/>边界验证]

        E2E_BUSINESS[业务流程测试<br/>Business Process Tests<br/>端到端验证]
        E2E_USER[用户场景测试<br/>User Scenario Tests<br/>用户体验验证]
    end

    subgraph "TDD工具链 (TDD Toolchain)"
        JUNIT[JUnit 5<br/>测试框架<br/>+ AssertJ]
        MOCKK[MockK<br/>Mock框架<br/>+ Kotlin DSL]
        TESTCONTAINERS[Testcontainers<br/>集成测试<br/>+ PostgreSQL]
        VITEST[Vitest<br/>前端测试框架<br/>+ Vue支持]
        VTU[Vue Testing Utils<br/>组件测试<br/>+ Composition API]
        PLAYWRIGHT[Playwright<br/>E2E测试<br/>+ TypeScript]
    end

    E2E --> E2E_BUSINESS
    E2E --> E2E_USER

    INTEGRATION --> INT_API
    INTEGRATION --> INT_DB
    INTEGRATION --> INT_CONTEXT

    UNIT --> UNIT_DOMAIN
    UNIT --> UNIT_SERVICE
    UNIT --> UNIT_COMPONENT
    
    UNIT_BACKEND --> JUNIT
    UNIT_BACKEND --> MOCKK
    
    INT_API --> TESTCONTAINERS
    INT_DB --> TESTCONTAINERS
    
    UNIT_FRONTEND --> VITEST
    INT_COMPONENT --> VTU
    
    E2E_BROWSER --> PLAYWRIGHT
    E2E_API --> PLAYWRIGHT
```

## 2. 后端测试实现

### 2.1 单元测试策略
```kotlin
// 领域模型单元测试
@ExtendWith(MockKExtension::class)
class ProcurementRequirementTest {
    
    @Test
    fun `should publish requirement when status is draft`() {
        // Given
        val requirement = ProcurementRequirement(
            id = RequirementId.generate(),
            buyerId = UserId.generate(),
            title = "测试需求",
            description = "测试描述",
            category = "ELECTRONICS",
            status = RequirementStatus.DRAFT,
            createdAt = Instant.now()
        )
        
        // When
        val event = requirement.publish()
        
        // Then
        assertThat(requirement.status).isEqualTo(RequirementStatus.PUBLISHED)
        assertThat(event).isInstanceOf(RequirementPublishedEvent::class.java)
        assertThat(event.requirementId).isEqualTo(requirement.id.value)
    }
    
    @Test
    fun `should throw exception when publishing non-draft requirement`() {
        // Given
        val requirement = ProcurementRequirement(
            id = RequirementId.generate(),
            buyerId = UserId.generate(),
            title = "测试需求",
            description = "测试描述",
            category = "ELECTRONICS",
            status = RequirementStatus.PUBLISHED,
            createdAt = Instant.now()
        )
        
        // When & Then
        assertThrows<IllegalArgumentException> {
            requirement.publish()
        }
    }
}

// 应用服务单元测试
@ExtendWith(MockKExtension::class)
class RequirementApplicationServiceTest {
    
    @MockK
    private lateinit var requirementRepository: RequirementRepository
    
    @MockK
    private lateinit var eventPublisher: DomainEventPublisher
    
    private lateinit var requirementService: RequirementApplicationService
    
    @BeforeEach
    fun setUp() {
        requirementService = RequirementApplicationService(requirementRepository, eventPublisher)
    }
    
    @Test
    fun `should create requirement successfully`() {
        // Given
        val command = CreateRequirementCommand(
            buyerId = UUID.randomUUID(),
            title = "测试需求",
            description = "测试描述",
            category = "ELECTRONICS",
            budget = Budget(BigDecimal("1000"), BigDecimal("2000"), "CNY"),
            deadline = LocalDate.now().plusDays(30),
            priority = RequirementPriority.MEDIUM
        )
        
        val savedRequirement = mockk<ProcurementRequirement>()
        every { savedRequirement.id } returns RequirementId.generate()
        every { requirementRepository.save(any()) } returns savedRequirement
        every { eventPublisher.publish(any()) } just Runs
        
        // When
        val result = requirementService.createRequirement(command)
        
        // Then
        assertThat(result).isNotNull
        verify { requirementRepository.save(any()) }
        verify { eventPublisher.publish(any<RequirementCreatedEvent>()) }
    }
    
    @Test
    fun `should validate command before creating requirement`() {
        // Given
        val invalidCommand = CreateRequirementCommand(
            buyerId = UUID.randomUUID(),
            title = "", // 空标题
            description = "测试描述",
            category = "ELECTRONICS",
            budget = null,
            deadline = LocalDate.now().minusDays(1), // 过期日期
            priority = RequirementPriority.MEDIUM
        )
        
        // When & Then
        assertThrows<ValidationException> {
            requirementService.createRequirement(invalidCommand)
        }
        
        verify(exactly = 0) { requirementRepository.save(any()) }
    }
}

// 值对象单元测试
class MoneyTest {
    
    @Test
    fun `should add money with same currency`() {
        // Given
        val money1 = Money(BigDecimal("100.00"), Currency.getInstance("CNY"))
        val money2 = Money(BigDecimal("50.00"), Currency.getInstance("CNY"))
        
        // When
        val result = money1 + money2
        
        // Then
        assertThat(result.amount).isEqualTo(BigDecimal("150.00"))
        assertThat(result.currency).isEqualTo(Currency.getInstance("CNY"))
    }
    
    @Test
    fun `should throw exception when adding money with different currencies`() {
        // Given
        val money1 = Money(BigDecimal("100.00"), Currency.getInstance("CNY"))
        val money2 = Money(BigDecimal("50.00"), Currency.getInstance("USD"))
        
        // When & Then
        assertThrows<IllegalArgumentException> {
            money1 + money2
        }
    }
    
    @Test
    fun `should not allow negative amount`() {
        // When & Then
        assertThrows<IllegalArgumentException> {
            Money(BigDecimal("-100.00"), Currency.getInstance("CNY"))
        }
    }
}
```

### 2.2 集成测试策略
```kotlin
// 仓储集成测试
@SpringBootTest
@Testcontainers
@Transactional
class RequirementRepositoryIntegrationTest {
    
    @Container
    companion object {
        @JvmStatic
        val postgres = PostgreSQLContainer<Nothing>("postgres:16").apply {
            withDatabaseName("test_db")
            withUsername("test")
            withPassword("test")
        }
    }
    
    @DynamicPropertySource
    companion object {
        @JvmStatic
        fun configureProperties(registry: DynamicPropertyRegistry) {
            registry.add("spring.datasource.url", postgres::getJdbcUrl)
            registry.add("spring.datasource.username", postgres::getUsername)
            registry.add("spring.datasource.password", postgres::getPassword)
        }
    }
    
    @Autowired
    private lateinit var requirementRepository: RequirementRepository
    
    @Autowired
    private lateinit var testEntityManager: TestEntityManager
    
    @Test
    fun `should save and find requirement by id`() {
        // Given
        val requirement = ProcurementRequirement(
            id = RequirementId.generate(),
            buyerId = UserId.generate(),
            title = "集成测试需求",
            description = "集成测试描述",
            category = "ELECTRONICS",
            status = RequirementStatus.DRAFT,
            createdAt = Instant.now()
        )
        
        // When
        val savedRequirement = requirementRepository.save(requirement)
        testEntityManager.flush()
        testEntityManager.clear()
        
        val foundRequirement = requirementRepository.findById(savedRequirement.id)
        
        // Then
        assertThat(foundRequirement).isNotNull
        assertThat(foundRequirement!!.title).isEqualTo("集成测试需求")
        assertThat(foundRequirement.status).isEqualTo(RequirementStatus.DRAFT)
    }
    
    @Test
    fun `should find requirements by buyer id and status`() {
        // Given
        val buyerId = UserId.generate()
        val requirement1 = createRequirement(buyerId, RequirementStatus.DRAFT)
        val requirement2 = createRequirement(buyerId, RequirementStatus.PUBLISHED)
        val requirement3 = createRequirement(UserId.generate(), RequirementStatus.DRAFT)
        
        requirementRepository.saveAll(listOf(requirement1, requirement2, requirement3))
        testEntityManager.flush()
        
        // When
        val draftRequirements = requirementRepository.findByBuyerIdAndStatus(
            buyerId, RequirementStatus.DRAFT
        )
        
        // Then
        assertThat(draftRequirements).hasSize(1)
        assertThat(draftRequirements[0].id).isEqualTo(requirement1.id)
    }
    
    private fun createRequirement(
        buyerId: UserId, 
        status: RequirementStatus
    ): ProcurementRequirement {
        return ProcurementRequirement(
            id = RequirementId.generate(),
            buyerId = buyerId,
            title = "测试需求",
            description = "测试描述",
            category = "ELECTRONICS",
            status = status,
            createdAt = Instant.now()
        )
    }
}

// API集成测试
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Testcontainers
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class RequirementControllerIntegrationTest {
    
    @Container
    companion object {
        @JvmStatic
        val postgres = PostgreSQLContainer<Nothing>("postgres:16")
    }
    
    @Autowired
    private lateinit var restTemplate: TestRestTemplate
    
    @Autowired
    private lateinit var requirementRepository: RequirementRepository
    
    @Test
    fun `should create requirement via API`() {
        // Given
        val request = CreateRequirementRequest(
            title = "API测试需求",
            description = "通过API创建的测试需求",
            category = "ELECTRONICS",
            budget = BudgetDto(
                minAmount = BigDecimal("1000"),
                maxAmount = BigDecimal("2000"),
                currency = "CNY"
            ),
            deadline = LocalDate.now().plusDays(30),
            priority = RequirementPriority.MEDIUM
        )
        
        val headers = HttpHeaders().apply {
            contentType = MediaType.APPLICATION_JSON
            setBearerAuth("test-jwt-token")
        }
        
        // When
        val response = restTemplate.exchange(
            "/api/requirements",
            HttpMethod.POST,
            HttpEntity(request, headers),
            object : ParameterizedTypeReference<ApiResponse<RequirementDto>>() {}
        )
        
        // Then
        assertThat(response.statusCode).isEqualTo(HttpStatus.CREATED)
        assertThat(response.body?.success).isTrue
        assertThat(response.body?.data?.title).isEqualTo("API测试需求")
        
        // 验证数据库中的数据
        val savedRequirements = requirementRepository.findAll()
        assertThat(savedRequirements).hasSize(1)
        assertThat(savedRequirements[0].title).isEqualTo("API测试需求")
    }
    
    @Test
    fun `should return validation error for invalid request`() {
        // Given
        val invalidRequest = CreateRequirementRequest(
            title = "", // 空标题
            description = "测试描述",
            category = "ELECTRONICS",
            budget = null,
            deadline = LocalDate.now().minusDays(1), // 过期日期
            priority = RequirementPriority.MEDIUM
        )
        
        val headers = HttpHeaders().apply {
            contentType = MediaType.APPLICATION_JSON
            setBearerAuth("test-jwt-token")
        }
        
        // When
        val response = restTemplate.exchange(
            "/api/requirements",
            HttpMethod.POST,
            HttpEntity(invalidRequest, headers),
            object : ParameterizedTypeReference<ApiResponse<Nothing>>() {}
        )
        
        // Then
        assertThat(response.statusCode).isEqualTo(HttpStatus.BAD_REQUEST)
        assertThat(response.body?.success).isFalse
        assertThat(response.body?.errors).isNotEmpty
    }
}
```

## 3. 前端测试实现

### 3.1 组件单元测试
```typescript
// Vue组件单元测试
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import { ElCard, ElButton, ElTag } from 'element-plus'
import RequirementCard from '../RequirementCard.vue'
import { Requirement, RequirementStatus } from '../../types'

const mockRequirement: Requirement = {
  id: '1',
  title: '测试需求',
  description: '这是一个测试需求的描述',
  category: 'ELECTRONICS',
  budget: {
    minAmount: 1000,
    maxAmount: 2000,
    currency: 'CNY'
  },
  status: RequirementStatus.DRAFT,
  priority: 'MEDIUM',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
};

const createWrapper = (props: any = {}) => {
  const pinia = createPinia()

  return mount(RequirementCard, {
    props: {
      requirement: mockRequirement,
      ...props
    },
    global: {
      plugins: [pinia],
      components: {
        ElCard,
        ElButton,
        ElTag
      }
    }
  })
}

describe('RequirementCard', () => {
  it('should render requirement information correctly', () => {
    const wrapper = createWrapper()

    expect(wrapper.text()).toContain('测试需求')
    expect(wrapper.text()).toContain('这是一个测试需求的描述')
    expect(wrapper.text()).toContain('ELECTRONICS')
    expect(wrapper.text()).toContain('¥1,000 - ¥2,000')
  })
  
  it('should emit edit event when edit button is clicked', async () => {
    const wrapper = createWrapper({ showActions: true })

    const editButton = wrapper.find('[data-testid="edit-button"]')
    await editButton.trigger('click')

    expect(wrapper.emitted('edit')).toBeTruthy()
    expect(wrapper.emitted('edit')?.[0]).toEqual([mockRequirement])
  })
  
  it('should emit delete event when delete button is clicked', async () => {
    const wrapper = createWrapper({ showActions: true })

    const deleteButton = wrapper.find('[data-testid="delete-button"]')
    await deleteButton.trigger('click')

    expect(wrapper.emitted('delete')).toBeTruthy()
    expect(wrapper.emitted('delete')?.[0]).toEqual([mockRequirement.id])
  })
  
  it('should not show actions when showActions is false', () => {
    const wrapper = createWrapper({ showActions: false })

    expect(wrapper.find('[data-testid="edit-button"]').exists()).toBe(false)
    expect(wrapper.find('[data-testid="delete-button"]').exists()).toBe(false)
  })
})

// Composable单元测试
import { createApp } from 'vue'
import { createPinia, setActivePinia } from 'pinia'
import { useRequirements } from '../useRequirements'

describe('useRequirements', () => {
  beforeEach(() => {
    const app = createApp({})
    const pinia = createPinia()
    app.use(pinia)
    setActivePinia(pinia)
  })

  it('should initialize with default values', () => {
    const { requirements, total, query } = useRequirements()

    expect(requirements.value).toEqual([])
    expect(total.value).toBe(0)
    expect(query.page).toBe(0)
    expect(query.size).toBe(20)
  })
  
  it('should update query when parameters change', () => {
    const { query } = useRequirements()

    query.page = 2
    query.keyword = '测试'

    expect(query.page).toBe(2)
    expect(query.keyword).toBe('测试')
  })
  
  it('should handle create requirement success', async () => {
    const { createRequirement } = useRequirements()

    const newRequirement = {
      title: '新需求',
      description: '新需求描述',
      category: 'ELECTRONICS',
      priority: 'MEDIUM' as const
    }

    // Mock API service
    vi.mock('@/services/requirement', () => ({
      requirementService: {
        createRequirement: vi.fn().mockResolvedValue(newRequirement)
      }
    }))

    await createRequirement(newRequirement)

    // 验证API调用
    expect(requirementService.createRequirement).toHaveBeenCalledWith(newRequirement)
  })
})
```

### 3.2 集成测试策略
```typescript
// API集成测试
import { rest } from 'msw';
import { setupServer } from 'msw/node';
import { render, screen, fireEvent, waitFor } from '@testing-library/vue';
import { createPinia } from 'pinia';
import { store } from '../../store';
import { RequirementList } from '../RequirementList';

// Mock服务器设置
const server = setupServer(
  rest.get('/api/requirements', (req, res, ctx) => {
    return res(
      ctx.json({
        success: true,
        data: {
          content: [
            {
              id: '1',
              title: '测试需求1',
              description: '测试描述1',
              category: 'ELECTRONICS',
              status: 'DRAFT'
            },
            {
              id: '2',
              title: '测试需求2',
              description: '测试描述2',
              category: 'OFFICE_SUPPLIES',
              status: 'PUBLISHED'
            }
          ],
          totalElements: 2,
          totalPages: 1,
          page: 0,
          size: 20
        }
      })
    );
  }),
  
  rest.post('/api/requirements', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        success: true,
        data: {
          id: '3',
          title: '新创建的需求',
          description: '新创建的描述',
          category: 'ELECTRONICS',
          status: 'DRAFT'
        }
      })
    );
  })
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

describe('RequirementList Integration', () => {
  it('should load and display requirements from API', async () => {
    render(
      <Provider store={store}>
        <RequirementList />
      </Provider>
    );
    
    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('测试需求1')).toBeInTheDocument();
      expect(screen.getByText('测试需求2')).toBeInTheDocument();
    });
    
    // 验证分页信息
    expect(screen.getByText('共 2 条')).toBeInTheDocument();
  });
  
  it('should handle API error gracefully', async () => {
    // 模拟API错误
    server.use(
      rest.get('/api/requirements', (req, res, ctx) => {
        return res(
          ctx.status(500),
          ctx.json({
            success: false,
            message: '服务器内部错误'
          })
        );
      })
    );
    
    render(
      <Provider store={store}>
        <RequirementList />
      </Provider>
    );
    
    await waitFor(() => {
      expect(screen.getByText('加载失败，请重试')).toBeInTheDocument();
    });
  });
});
```

## 4. 端到端测试实现

### 4.1 Playwright E2E测试
```typescript
// e2e/requirement-management.spec.ts
import { test, expect } from '@playwright/test';

test.describe('需求管理流程', () => {
  test.beforeEach(async ({ page }) => {
    // 登录
    await page.goto('/login');
    await page.fill('[data-testid=username]', '<EMAIL>');
    await page.fill('[data-testid=password]', 'password123');
    await page.click('[data-testid=login-button]');
    
    // 等待登录完成
    await expect(page).toHaveURL('/dashboard');
  });
  
  test('应该能够创建新需求', async ({ page }) => {
    // 导航到需求管理页面
    await page.click('[data-testid=nav-requirements]');
    await expect(page).toHaveURL('/requirements');
    
    // 点击创建需求按钮
    await page.click('[data-testid=create-requirement-button]');
    
    // 填写需求表单
    await page.fill('[data-testid=requirement-title]', 'E2E测试需求');
    await page.fill('[data-testid=requirement-description]', '这是一个端到端测试创建的需求');
    await page.selectOption('[data-testid=requirement-category]', 'ELECTRONICS');
    await page.fill('[data-testid=budget-min]', '1000');
    await page.fill('[data-testid=budget-max]', '2000');
    
    // 提交表单
    await page.click('[data-testid=submit-requirement]');
    
    // 验证成功消息
    await expect(page.locator('.ant-message-success')).toContainText('需求创建成功');
    
    // 验证需求出现在列表中
    await expect(page.locator('[data-testid=requirement-list]')).toContainText('E2E测试需求');
  });
  
  test('应该能够发布需求', async ({ page }) => {
    // 假设已有草稿需求
    await page.goto('/requirements');
    
    // 找到草稿状态的需求
    const draftRequirement = page.locator('[data-testid=requirement-card]').filter({
      has: page.locator('[data-testid=requirement-status]:has-text("草稿")')
    }).first();
    
    // 点击发布按钮
    await draftRequirement.locator('[data-testid=publish-button]').click();
    
    // 确认发布
    await page.click('[data-testid=confirm-publish]');
    
    // 验证状态变更
    await expect(draftRequirement.locator('[data-testid=requirement-status]')).toContainText('已发布');
  });
  
  test('应该能够搜索和筛选需求', async ({ page }) => {
    await page.goto('/requirements');
    
    // 使用搜索功能
    await page.fill('[data-testid=search-input]', 'E2E测试');
    await page.press('[data-testid=search-input]', 'Enter');
    
    // 验证搜索结果
    await expect(page.locator('[data-testid=requirement-list]')).toContainText('E2E测试需求');
    
    // 使用分类筛选
    await page.selectOption('[data-testid=category-filter]', 'ELECTRONICS');
    
    // 验证筛选结果
    const requirementCards = page.locator('[data-testid=requirement-card]');
    await expect(requirementCards).toHaveCount(1);
  });
});

// e2e/supplier-interaction.spec.ts
test.describe('供应商交互流程', () => {
  test('完整的供应商发现和联系流程', async ({ page }) => {
    // 登录为买家
    await page.goto('/login');
    await page.fill('[data-testid=username]', '<EMAIL>');
    await page.fill('[data-testid=password]', 'password123');
    await page.click('[data-testid=login-button]');
    
    // 浏览供应商
    await page.click('[data-testid=nav-suppliers]');
    await expect(page).toHaveURL('/suppliers');
    
    // 查看供应商详情
    await page.click('[data-testid=supplier-card]').first();
    
    // 联系供应商
    await page.click('[data-testid=contact-supplier-button]');
    
    // 填写联系表单
    await page.fill('[data-testid=contact-message]', '我对您的产品很感兴趣，希望了解更多详情。');
    await page.click('[data-testid=send-message-button]');
    
    // 验证成功消息
    await expect(page.locator('.ant-message-success')).toContainText('消息发送成功');
  });
});
```

### 4.2 性能测试
```typescript
// performance/load-test.spec.ts
import { test, expect } from '@playwright/test';

test.describe('性能测试', () => {
  test('首页加载性能', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/');
    
    // 等待关键元素加载
    await expect(page.locator('[data-testid=main-content]')).toBeVisible();
    
    const loadTime = Date.now() - startTime;
    
    // 验证加载时间小于3秒
    expect(loadTime).toBeLessThan(3000);
  });
  
  test('需求列表分页性能', async ({ page }) => {
    await page.goto('/requirements');
    
    // 测试分页响应时间
    const startTime = Date.now();
    
    await page.click('[data-testid=pagination-next]');
    await expect(page.locator('[data-testid=requirement-list]')).toBeVisible();
    
    const responseTime = Date.now() - startTime;
    
    // 验证分页响应时间小于1秒
    expect(responseTime).toBeLessThan(1000);
  });
});
```

## 5. 测试配置和工具

### 5.1 Vitest配置
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    globals: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        'src/main.ts',
        'src/**/*.stories.{ts,vue}',
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@pages': resolve(__dirname, 'src/pages'),
      '@composables': resolve(__dirname, 'src/composables'),
      '@services': resolve(__dirname, 'src/services'),
      '@stores': resolve(__dirname, 'src/stores'),
      '@types': resolve(__dirname, 'src/types'),
      '@utils': resolve(__dirname, 'src/utils'),
    },
  },
})
```

### 5.2 Playwright配置
```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['junit', { outputFile: 'test-results/junit.xml' }],
  ],
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

## 8. TDD实施策略

### 8.1 TDD开发节奏

#### 每日TDD工作流
```
09:00-09:30  回顾昨日测试，运行完整测试套件
09:30-12:00  TDD开发第一轮（红-绿-重构 × 3-5次）
12:00-13:00  午休
13:00-16:00  TDD开发第二轮（红-绿-重构 × 3-5次）
16:00-16:30  代码审查和重构优化
16:30-17:00  集成测试和文档更新
17:00-17:30  测试覆盖率检查和CI/CD验证
```

#### TDD时间分配
- **编写测试**：40%的开发时间
- **编写实现**：35%的开发时间
- **重构优化**：20%的开发时间
- **测试维护**：5%的开发时间

### 8.2 TDD质量指标

#### 测试覆盖率目标
```kotlin
// 覆盖率配置
jacoco {
    toolVersion = "0.8.8"
}

jacocoTestReport {
    reports {
        xml.required = true
        html.required = true
    }

    executionData.setFrom(fileTree(dir: project.buildDir, includes: ['**/*.exec']))

    finalizedBy jacocoTestCoverageVerification
}

jacocoTestCoverageVerification {
    violationRules {
        rule {
            limit {
                counter = 'LINE'
                value = 'COVEREDRATIO'
                minimum = 0.95  // 领域模型95%覆盖率
            }
        }
        rule {
            limit {
                counter = 'BRANCH'
                value = 'COVEREDRATIO'
                minimum = 0.90  // 分支覆盖率90%
            }
        }
    }
}
```

#### 测试质量指标
- **领域模型测试覆盖率**：≥95%
- **应用服务测试覆盖率**：≥90%
- **API控制器测试覆盖率**：≥85%
- **前端组件测试覆盖率**：≥80%
- **测试执行时间**：单元测试<5秒，集成测试<30秒
- **测试稳定性**：测试成功率≥99%

### 8.3 TDD最佳实践

#### 测试命名约定
```kotlin
// 后端测试命名模式：should_[期望结果]_when_[条件]
class ProcurementRequirementTest {
    @Test
    fun `should create requirement when all required fields are provided`()

    @Test
    fun `should throw exception when title is empty`()

    @Test
    fun `should publish requirement when status is draft`()

    @Test
    fun `should not allow publishing when budget exceeds approval limit without approval`()
}
```

```typescript
// 前端测试命名模式：should [期望结果] when [条件]
describe('RequirementForm', () => {
  it('should display validation errors when required fields are empty')
  it('should emit submit event when form is valid')
  it('should disable submit button when form is submitting')
  it('should reset form when reset button is clicked')
})
```

#### TDD代码审查清单
- [ ] 测试是否先于实现代码编写？
- [ ] 测试是否清晰表达了业务需求？
- [ ] 实现代码是否是让测试通过的最少代码？
- [ ] 重构是否在测试保护下进行？
- [ ] 测试是否具有良好的可读性和可维护性？
- [ ] 是否遵循了AAA模式（Arrange-Act-Assert）？
- [ ] Mock使用是否合理，避免过度Mock？
- [ ] 测试数据是否具有代表性？

### 8.4 TDD工具配置优化

#### IntelliJ IDEA TDD配置
```xml
<!-- .idea/runConfigurations/All_Tests.xml -->
<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="All Tests" type="JUnit" factoryName="JUnit">
    <option name="PACKAGE_NAME" value="" />
    <option name="MAIN_CLASS_NAME" value="" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="package" />
    <option name="VM_PARAMETERS" value="-ea" />
    <option name="PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$MODULE_DIR$" />
    <option name="PASS_PARENT_ENVS" value="true" />
    <option name="TEST_SEARCH_SCOPE">
      <value defaultName="singleModule" />
    </option>
    <patterns />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>
```

#### VS Code TDD配置
```json
// .vscode/settings.json
{
  "vitest.enable": true,
  "vitest.commandLine": "npm run test",
  "testing.automaticallyOpenPeekView": "never",
  "testing.followRunningTest": true,
  "testing.openTesting": "neverOpen",
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "editor.codeActionsOnSave": {
    "source.organizeImports": true,
    "source.fixAll.eslint": true
  }
}

// .vscode/tasks.json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Run Tests",
      "type": "shell",
      "command": "npm run test",
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      }
    },
    {
      "label": "Run Tests with Coverage",
      "type": "shell",
      "command": "npm run test:coverage",
      "group": "test"
    }
  ]
}
```

### 8.5 TDD持续改进

#### 测试度量和监控
```kotlin
// 测试执行时间监控
@TestMethodOrder(OrderAnnotation::class)
class PerformanceTest {

    @Test
    @Order(1)
    @Timeout(value = 5, unit = TimeUnit.SECONDS)
    fun `unit tests should complete within 5 seconds`() {
        // 运行所有单元测试
        val result = JUnitPlatform.run(UnitTestSuite::class.java)
        assertThat(result.wasSuccessful()).isTrue()
    }

    @Test
    @Order(2)
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    fun `integration tests should complete within 30 seconds`() {
        // 运行所有集成测试
        val result = JUnitPlatform.run(IntegrationTestSuite::class.java)
        assertThat(result.wasSuccessful()).isTrue()
    }
}
```

#### TDD反思和改进
- **每周回顾**：分析测试覆盖率变化和测试质量
- **每月优化**：重构测试代码，提升测试效率
- **季度评估**：评估TDD实践效果，调整策略
- **持续学习**：学习新的测试技术和最佳实践

## 9. TDD成功要素

### 9.1 技术要素
- **快速反馈**：测试执行时间控制在秒级
- **工具支持**：IDE集成、自动化运行、覆盖率报告
- **环境隔离**：测试环境与开发环境完全隔离
- **数据管理**：测试数据的创建、清理和隔离

### 9.2 实践要素
- **纪律性**：严格遵循红-绿-重构循环
- **小步快跑**：每次只实现一个小功能
- **持续重构**：在测试保护下持续改进代码
- **测试优先**：始终先写测试再写实现

### 9.3 心理要素
- **耐心**：接受TDD初期的学习曲线
- **信心**：相信测试能够保证代码质量
- **坚持**：在压力下也要坚持TDD实践
- **改进**：持续学习和改进TDD技能

这套TDD测试策略为超级个人全栈开发者提供了完整的测试驱动开发解决方案，通过红-绿-重构的循环，确保在保持17个限界上下文复杂度的同时，实现高质量、高可靠性的全栈开发。
