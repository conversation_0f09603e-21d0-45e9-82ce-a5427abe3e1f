# 超级个人全栈开发 - 数据库设计文档

## 1. 数据库架构总览

### 1.1 PostgreSQL统一数据库设计理念
- **Schema分离**：17个限界上下文使用独立Schema，保持边界清晰
- **统一管理**：单一PostgreSQL实例，简化运维和备份
- **性能优化**：针对单体应用优化的索引和查询策略
- **事务一致性**：利用PostgreSQL的ACID特性保证数据一致性
- **扩展性**：支持后续的读写分离和分库分表
- **架构演进**：支持用户域重构、智能域扩展、治理风控域新增

### 1.2 Schema架构图
```mermaid
graph TB
    subgraph "PostgreSQL 16 Database"
        subgraph "交易前置域 Schemas"
            REQ_SCHEMA[requirement_mgmt<br/>需求管理]
            SUP_SCHEMA[supplier_discovery<br/>供应商发现]
            BID_SCHEMA[bidding_evaluation<br/>竞价评估]
        end
        
        subgraph "交易执行域 Schemas"
            ORD_SCHEMA[order_fulfillment<br/>订单履约]
            LOG_SCHEMA[logistics_service<br/>物流服务]
            PAY_SCHEMA[payment_settlement<br/>支付结算]
        end
        
        subgraph "交易后置域 Schemas"
            INV_SCHEMA[inventory_operations<br/>库存运营]
            REL_SCHEMA[supplier_relationship<br/>供应商关系]
            REP_SCHEMA[intelligent_replenishment<br/>智能补货]
        end
        
        subgraph "用户服务域 Schemas (重构优化)"
            USR_INS_SCHEMA[user_insights<br/>用户洞察]
            USR_GRW_SCHEMA[user_growth<br/>用户增长]
        end

        subgraph "数据智能域 Schemas (扩展优化)"
            ANA_SCHEMA[data_analytics<br/>数据分析]
            REC_SCHEMA[intelligent_recommendation<br/>智能推荐]
            DEC_SCHEMA[intelligent_decision<br/>智能决策]
        end

        subgraph "平台服务域 Schemas"
            IAM_SCHEMA[identity_access<br/>身份权限]
            COM_SCHEMA[communication<br/>通信协作]
        end

        subgraph "治理风控域 Schemas (新增)"
            COMP_SCHEMA[compliance_management<br/>合规管理]
            RISK_SCHEMA[risk_control<br/>风险控制]
        end
        
        subgraph "共享 Schemas"
            SHARED_SCHEMA[shared<br/>共享数据]
            EVENT_SCHEMA[event_store<br/>事件存储]
            AUDIT_SCHEMA[audit_log<br/>审计日志]
        end
    end
```

## 2. 核心Schema设计

### 2.1 需求管理Schema (requirement_mgmt)
```sql
-- 需求管理Schema
CREATE SCHEMA IF NOT EXISTS requirement_mgmt;

-- 采购需求表
CREATE TABLE requirement_mgmt.procurement_requirements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    buyer_id UUID NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL,
    budget_min_amount DECIMAL(15,2),
    budget_max_amount DECIMAL(15,2),
    budget_currency VARCHAR(3) DEFAULT 'CNY',
    deadline DATE,
    status VARCHAR(20) DEFAULT 'DRAFT',
    priority VARCHAR(10) DEFAULT 'MEDIUM',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    version INTEGER DEFAULT 1,
    
    CONSTRAINT chk_budget_range CHECK (
        budget_min_amount IS NULL OR budget_max_amount IS NULL OR 
        budget_min_amount <= budget_max_amount
    ),
    CONSTRAINT chk_status CHECK (status IN ('DRAFT', 'PUBLISHED', 'IN_BIDDING', 'CLOSED', 'CANCELLED')),
    CONSTRAINT chk_priority CHECK (priority IN ('LOW', 'MEDIUM', 'HIGH', 'URGENT'))
);

-- 需求规格表
CREATE TABLE requirement_mgmt.requirement_specifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    requirement_id UUID NOT NULL REFERENCES requirement_mgmt.procurement_requirements(id) ON DELETE CASCADE,
    spec_name VARCHAR(100) NOT NULL,
    spec_value TEXT NOT NULL,
    is_mandatory BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 需求附件表
CREATE TABLE requirement_mgmt.requirement_attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    requirement_id UUID NOT NULL REFERENCES requirement_mgmt.procurement_requirements(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引优化
CREATE INDEX idx_requirements_buyer_id ON requirement_mgmt.procurement_requirements(buyer_id);
CREATE INDEX idx_requirements_category ON requirement_mgmt.procurement_requirements(category);
CREATE INDEX idx_requirements_status ON requirement_mgmt.procurement_requirements(status);
CREATE INDEX idx_requirements_created_at ON requirement_mgmt.procurement_requirements(created_at);
CREATE INDEX idx_requirements_deadline ON requirement_mgmt.procurement_requirements(deadline);
```

### 2.2 供应商发现Schema (supplier_discovery)
```sql
-- 供应商发现Schema
CREATE SCHEMA IF NOT EXISTS supplier_discovery;

-- 供应商能力画像表
CREATE TABLE supplier_discovery.supplier_capabilities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    supplier_id UUID NOT NULL,
    company_name VARCHAR(200) NOT NULL,
    business_license VARCHAR(50),
    established_year INTEGER,
    employee_count INTEGER,
    annual_revenue DECIMAL(15,2),
    main_products TEXT[],
    service_regions TEXT[],
    certifications TEXT[],
    production_capacity JSONB,
    quality_score DECIMAL(3,2) DEFAULT 0.00,
    delivery_score DECIMAL(3,2) DEFAULT 0.00,
    service_score DECIMAL(3,2) DEFAULT 0.00,
    overall_score DECIMAL(3,2) DEFAULT 0.00,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_quality_score CHECK (quality_score >= 0 AND quality_score <= 5),
    CONSTRAINT chk_delivery_score CHECK (delivery_score >= 0 AND delivery_score <= 5),
    CONSTRAINT chk_service_score CHECK (service_score >= 0 AND service_score <= 5),
    CONSTRAINT chk_overall_score CHECK (overall_score >= 0 AND overall_score <= 5)
);

-- 供应商产品目录表
CREATE TABLE supplier_discovery.supplier_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    supplier_id UUID NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    product_code VARCHAR(50),
    category VARCHAR(50) NOT NULL,
    description TEXT,
    specifications JSONB,
    unit_price DECIMAL(15,2),
    currency VARCHAR(3) DEFAULT 'CNY',
    min_order_quantity INTEGER,
    lead_time_days INTEGER,
    images TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 供应商匹配记录表
CREATE TABLE supplier_discovery.supplier_matches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    requirement_id UUID NOT NULL,
    supplier_id UUID NOT NULL,
    match_score DECIMAL(5,4) NOT NULL,
    match_factors JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_match_score CHECK (match_score >= 0 AND match_score <= 1)
);

-- 索引优化
CREATE INDEX idx_capabilities_supplier_id ON supplier_discovery.supplier_capabilities(supplier_id);
CREATE INDEX idx_capabilities_overall_score ON supplier_discovery.supplier_capabilities(overall_score DESC);
CREATE INDEX idx_products_supplier_id ON supplier_discovery.supplier_products(supplier_id);
CREATE INDEX idx_products_category ON supplier_discovery.supplier_products(category);
CREATE INDEX idx_matches_requirement_id ON supplier_discovery.supplier_matches(requirement_id);
CREATE INDEX idx_matches_score ON supplier_discovery.supplier_matches(match_score DESC);
```

### 2.3 订单履约Schema (order_fulfillment)
```sql
-- 订单履约Schema
CREATE SCHEMA IF NOT EXISTS order_fulfillment;

-- 订单表
CREATE TABLE order_fulfillment.orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_number VARCHAR(50) UNIQUE NOT NULL,
    requirement_id UUID NOT NULL,
    bid_id UUID,
    buyer_id UUID NOT NULL,
    supplier_id UUID NOT NULL,
    order_type VARCHAR(20) DEFAULT 'FORMAL',
    total_amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'CNY',
    status VARCHAR(20) DEFAULT 'PENDING',
    payment_terms VARCHAR(50),
    delivery_terms VARCHAR(50),
    expected_delivery_date DATE,
    actual_delivery_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    confirmed_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    version INTEGER DEFAULT 1,
    
    CONSTRAINT chk_order_type CHECK (order_type IN ('SAMPLE', 'FORMAL')),
    CONSTRAINT chk_order_status CHECK (status IN (
        'PENDING', 'CONFIRMED', 'IN_PRODUCTION', 'SHIPPED', 
        'DELIVERED', 'COMPLETED', 'CANCELLED'
    ))
);

-- 订单项表
CREATE TABLE order_fulfillment.order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES order_fulfillment.orders(id) ON DELETE CASCADE,
    product_name VARCHAR(200) NOT NULL,
    product_code VARCHAR(50),
    specifications JSONB,
    quantity INTEGER NOT NULL,
    unit VARCHAR(20) NOT NULL,
    unit_price DECIMAL(15,2) NOT NULL,
    total_price DECIMAL(15,2) NOT NULL,
    delivered_quantity INTEGER DEFAULT 0,
    
    CONSTRAINT chk_quantity CHECK (quantity > 0),
    CONSTRAINT chk_unit_price CHECK (unit_price >= 0),
    CONSTRAINT chk_delivered_quantity CHECK (delivered_quantity >= 0 AND delivered_quantity <= quantity)
);

-- 订单状态历史表
CREATE TABLE order_fulfillment.order_status_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES order_fulfillment.orders(id) ON DELETE CASCADE,
    from_status VARCHAR(20),
    to_status VARCHAR(20) NOT NULL,
    changed_by UUID,
    change_reason TEXT,
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引优化
CREATE INDEX idx_orders_buyer_id ON order_fulfillment.orders(buyer_id);
CREATE INDEX idx_orders_supplier_id ON order_fulfillment.orders(supplier_id);
CREATE INDEX idx_orders_status ON order_fulfillment.orders(status);
CREATE INDEX idx_orders_created_at ON order_fulfillment.orders(created_at);
CREATE INDEX idx_order_items_order_id ON order_fulfillment.order_items(order_id);
```

### 2.4 智能补货Schema (intelligent_replenishment)
```sql
-- 智能补货Schema
CREATE SCHEMA IF NOT EXISTS intelligent_replenishment;

-- 补货策略表
CREATE TABLE intelligent_replenishment.replenishment_strategies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    buyer_id UUID NOT NULL,
    product_code VARCHAR(50) NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    auto_replenishment_enabled BOOLEAN DEFAULT false,
    reorder_point INTEGER NOT NULL,
    safety_stock INTEGER NOT NULL,
    max_stock_level INTEGER,
    preferred_order_quantity INTEGER,
    max_unit_price DECIMAL(15,2),
    currency VARCHAR(3) DEFAULT 'CNY',
    lead_time_days INTEGER DEFAULT 30,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(buyer_id, product_code)
);

-- 信任供应商表
CREATE TABLE intelligent_replenishment.trusted_suppliers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    strategy_id UUID NOT NULL REFERENCES intelligent_replenishment.replenishment_strategies(id) ON DELETE CASCADE,
    supplier_id UUID NOT NULL,
    trust_score DECIMAL(3,2) NOT NULL,
    priority_rank INTEGER NOT NULL,
    last_order_date DATE,
    total_orders INTEGER DEFAULT 0,
    average_unit_price DECIMAL(15,2),
    average_delivery_days INTEGER,
    quality_rating DECIMAL(3,2),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_trust_score CHECK (trust_score >= 0 AND trust_score <= 1),
    CONSTRAINT chk_quality_rating CHECK (quality_rating >= 0 AND quality_rating <= 5)
);

-- 补货执行记录表
CREATE TABLE intelligent_replenishment.replenishment_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    strategy_id UUID NOT NULL REFERENCES intelligent_replenishment.replenishment_strategies(id),
    trigger_type VARCHAR(20) NOT NULL,
    trigger_inventory_level INTEGER,
    recommended_quantity INTEGER NOT NULL,
    actual_quantity INTEGER,
    recommended_supplier_id UUID,
    selected_supplier_id UUID,
    estimated_unit_price DECIMAL(15,2),
    actual_unit_price DECIMAL(15,2),
    order_id UUID,
    execution_status VARCHAR(20) DEFAULT 'PENDING',
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT chk_trigger_type CHECK (trigger_type IN ('AUTO', 'MANUAL', 'SCHEDULED')),
    CONSTRAINT chk_execution_status CHECK (execution_status IN (
        'PENDING', 'APPROVED', 'ORDERED', 'COMPLETED', 'CANCELLED', 'FAILED'
    ))
);

-- 索引优化
CREATE INDEX idx_strategies_buyer_id ON intelligent_replenishment.replenishment_strategies(buyer_id);
CREATE INDEX idx_strategies_product_code ON intelligent_replenishment.replenishment_strategies(product_code);
CREATE INDEX idx_trusted_suppliers_strategy_id ON intelligent_replenishment.trusted_suppliers(strategy_id);
CREATE INDEX idx_trusted_suppliers_score ON intelligent_replenishment.trusted_suppliers(trust_score DESC);
CREATE INDEX idx_executions_strategy_id ON intelligent_replenishment.replenishment_executions(strategy_id);
CREATE INDEX idx_executions_status ON intelligent_replenishment.replenishment_executions(execution_status);
```

## 3. JPA实体映射设计

### 3.1 需求管理实体映射
```kotlin
// 采购需求聚合根
@Entity
@Table(schema = "requirement_mgmt", name = "procurement_requirements")
class ProcurementRequirement(
    @Id
    @Column(name = "id")
    val id: UUID = UUID.randomUUID(),
    
    @Column(name = "buyer_id", nullable = false)
    val buyerId: UUID,
    
    @Column(name = "title", nullable = false, length = 200)
    var title: String,
    
    @Column(name = "description", columnDefinition = "TEXT")
    var description: String?,
    
    @Column(name = "category", nullable = false, length = 50)
    var category: String,
    
    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "minAmount", column = Column(name = "budget_min_amount")),
        AttributeOverride(name = "maxAmount", column = Column(name = "budget_max_amount")),
        AttributeOverride(name = "currency", column = Column(name = "budget_currency"))
    )
    var budget: Budget?,
    
    @Column(name = "deadline")
    var deadline: LocalDate?,
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    var status: RequirementStatus = RequirementStatus.DRAFT,
    
    @Enumerated(EnumType.STRING)
    @Column(name = "priority", nullable = false)
    var priority: RequirementPriority = RequirementPriority.MEDIUM,
    
    @Column(name = "created_at", nullable = false)
    val createdAt: Instant = Instant.now(),
    
    @Column(name = "updated_at", nullable = false)
    var updatedAt: Instant = Instant.now(),
    
    @Version
    @Column(name = "version")
    var version: Int = 1,
    
    @OneToMany(mappedBy = "requirement", cascade = [CascadeType.ALL], orphanRemoval = true)
    private val _specifications: MutableList<RequirementSpecification> = mutableListOf(),
    
    @OneToMany(mappedBy = "requirement", cascade = [CascadeType.ALL], orphanRemoval = true)
    private val _attachments: MutableList<RequirementAttachment> = mutableListOf()
) {
    // 提供不可变视图
    val specifications: List<RequirementSpecification> get() = _specifications.toList()
    val attachments: List<RequirementAttachment> get() = _attachments.toList()
    
    // 领域行为
    fun publish(): RequirementPublishedEvent {
        require(status == RequirementStatus.DRAFT) { "只能发布草稿状态的需求" }
        status = RequirementStatus.PUBLISHED
        updatedAt = Instant.now()
        return RequirementPublishedEvent(id, buyerId, category)
    }
    
    fun addSpecification(name: String, value: String, mandatory: Boolean = true) {
        _specifications.add(RequirementSpecification(
            requirement = this,
            specName = name,
            specValue = value,
            isMandatory = mandatory
        ))
        updatedAt = Instant.now()
    }
}

// 需求规格实体
@Entity
@Table(schema = "requirement_mgmt", name = "requirement_specifications")
class RequirementSpecification(
    @Id
    @Column(name = "id")
    val id: UUID = UUID.randomUUID(),
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "requirement_id", nullable = false)
    val requirement: ProcurementRequirement,
    
    @Column(name = "spec_name", nullable = false, length = 100)
    val specName: String,
    
    @Column(name = "spec_value", nullable = false, columnDefinition = "TEXT")
    val specValue: String,
    
    @Column(name = "is_mandatory", nullable = false)
    val isMandatory: Boolean = true,
    
    @Column(name = "created_at", nullable = false)
    val createdAt: Instant = Instant.now()
)

// 预算值对象
@Embeddable
data class Budget(
    @Column(name = "min_amount", precision = 15, scale = 2)
    val minAmount: BigDecimal?,
    
    @Column(name = "max_amount", precision = 15, scale = 2)
    val maxAmount: BigDecimal?,
    
    @Column(name = "currency", length = 3)
    val currency: String = "CNY"
) {
    init {
        if (minAmount != null && maxAmount != null) {
            require(minAmount <= maxAmount) { "最小预算不能大于最大预算" }
        }
    }
}
```

### 3.2 智能补货实体映射
```kotlin
// 补货策略聚合根
@Entity
@Table(schema = "intelligent_replenishment", name = "replenishment_strategies")
class ReplenishmentStrategy(
    @Id
    @Column(name = "id")
    val id: UUID = UUID.randomUUID(),
    
    @Column(name = "buyer_id", nullable = false)
    val buyerId: UUID,
    
    @Column(name = "product_code", nullable = false, length = 50)
    val productCode: String,
    
    @Column(name = "product_name", nullable = false, length = 200)
    val productName: String,
    
    @Column(name = "auto_replenishment_enabled", nullable = false)
    var autoReplenishmentEnabled: Boolean = false,
    
    @Column(name = "reorder_point", nullable = false)
    var reorderPoint: Int,
    
    @Column(name = "safety_stock", nullable = false)
    var safetyStock: Int,
    
    @Column(name = "max_stock_level")
    var maxStockLevel: Int?,
    
    @Column(name = "preferred_order_quantity")
    var preferredOrderQuantity: Int?,
    
    @Column(name = "max_unit_price", precision = 15, scale = 2)
    var maxUnitPrice: BigDecimal?,
    
    @Column(name = "currency", length = 3)
    var currency: String = "CNY",
    
    @Column(name = "lead_time_days")
    var leadTimeDays: Int = 30,
    
    @Column(name = "created_at", nullable = false)
    val createdAt: Instant = Instant.now(),
    
    @Column(name = "updated_at", nullable = false)
    var updatedAt: Instant = Instant.now(),
    
    @OneToMany(mappedBy = "strategy", cascade = [CascadeType.ALL], orphanRemoval = true)
    private val _trustedSuppliers: MutableList<TrustedSupplier> = mutableListOf()
) {
    val trustedSuppliers: List<TrustedSupplier> get() = _trustedSuppliers.toList()
    
    fun executeOneClickReplenishment(
        quantity: Int,
        maxUnitPrice: BigDecimal
    ): OneClickReplenishmentExecutedEvent {
        val bestSupplier = selectBestTrustedSupplier()
        require(bestSupplier != null) { "没有可用的信任供应商" }
        require(bestSupplier.trustScore >= BigDecimal("0.7")) { "供应商信任度不足" }
        
        return OneClickReplenishmentExecutedEvent(
            strategyId = id,
            buyerId = buyerId,
            supplierId = bestSupplier.supplierId,
            productCode = productCode,
            quantity = quantity,
            maxUnitPrice = maxUnitPrice
        )
    }
    
    private fun selectBestTrustedSupplier(): TrustedSupplier? {
        return _trustedSuppliers
            .filter { it.isActive && it.trustScore >= BigDecimal("0.7") }
            .maxByOrNull { it.calculateOverallScore() }
    }
}

// 信任供应商实体
@Entity
@Table(schema = "intelligent_replenishment", name = "trusted_suppliers")
class TrustedSupplier(
    @Id
    @Column(name = "id")
    val id: UUID = UUID.randomUUID(),
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "strategy_id", nullable = false)
    val strategy: ReplenishmentStrategy,
    
    @Column(name = "supplier_id", nullable = false)
    val supplierId: UUID,
    
    @Column(name = "trust_score", nullable = false, precision = 3, scale = 2)
    var trustScore: BigDecimal,
    
    @Column(name = "priority_rank", nullable = false)
    var priorityRank: Int,
    
    @Column(name = "last_order_date")
    var lastOrderDate: LocalDate?,
    
    @Column(name = "total_orders", nullable = false)
    var totalOrders: Int = 0,
    
    @Column(name = "average_unit_price", precision = 15, scale = 2)
    var averageUnitPrice: BigDecimal?,
    
    @Column(name = "average_delivery_days")
    var averageDeliveryDays: Int?,
    
    @Column(name = "quality_rating", precision = 3, scale = 2)
    var qualityRating: BigDecimal?,
    
    @Column(name = "is_active", nullable = false)
    var isActive: Boolean = true,
    
    @Column(name = "created_at", nullable = false)
    val createdAt: Instant = Instant.now(),
    
    @Column(name = "updated_at", nullable = false)
    var updatedAt: Instant = Instant.now()
) {
    fun calculateOverallScore(): BigDecimal {
        val trustWeight = BigDecimal("0.4")
        val qualityWeight = BigDecimal("0.3")
        val deliveryWeight = BigDecimal("0.2")
        val recentActivityWeight = BigDecimal("0.1")
        
        val qualityScore = qualityRating?.divide(BigDecimal("5")) ?: BigDecimal.ZERO
        val deliveryScore = calculateDeliveryScore()
        val recentActivityScore = calculateRecentActivityScore()
        
        return trustScore.multiply(trustWeight)
            .add(qualityScore.multiply(qualityWeight))
            .add(deliveryScore.multiply(deliveryWeight))
            .add(recentActivityScore.multiply(recentActivityWeight))
    }
    
    private fun calculateDeliveryScore(): BigDecimal {
        return if (averageDeliveryDays != null && averageDeliveryDays!! <= 30) {
            BigDecimal.ONE.subtract(BigDecimal(averageDeliveryDays!!).divide(BigDecimal("30")))
        } else {
            BigDecimal.ZERO
        }
    }
    
    private fun calculateRecentActivityScore(): BigDecimal {
        return if (lastOrderDate != null) {
            val daysSinceLastOrder = ChronoUnit.DAYS.between(lastOrderDate, LocalDate.now())
            if (daysSinceLastOrder <= 90) {
                BigDecimal.ONE.subtract(BigDecimal(daysSinceLastOrder).divide(BigDecimal("90")))
            } else {
                BigDecimal.ZERO
            }
        } else {
            BigDecimal.ZERO
        }
    }
}
```

## 4. 数据库性能优化

### 4.1 连接池配置
```yaml
# application.yml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
      
  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        jdbc:
          batch_size: 25
        order_inserts: true
        order_updates: true
        jdbc.batch_versioned_data: true
        cache:
          use_second_level_cache: true
          use_query_cache: true
```

### 4.2 查询优化策略
```kotlin
// 仓储实现中的查询优化
@Repository
class RequirementRepositoryImpl(
    private val entityManager: EntityManager
) : RequirementRepository {
    
    fun findRequirementsWithPagination(
        buyerId: UUID?,
        category: String?,
        status: RequirementStatus?,
        pageable: Pageable
    ): Page<ProcurementRequirement> {
        
        val cb = entityManager.criteriaBuilder
        val query = cb.createQuery(ProcurementRequirement::class.java)
        val root = query.from(ProcurementRequirement::class.java)
        
        val predicates = mutableListOf<Predicate>()
        
        buyerId?.let { predicates.add(cb.equal(root.get<UUID>("buyerId"), it)) }
        category?.let { predicates.add(cb.equal(root.get<String>("category"), it)) }
        status?.let { predicates.add(cb.equal(root.get<RequirementStatus>("status"), it)) }
        
        if (predicates.isNotEmpty()) {
            query.where(*predicates.toTypedArray())
        }
        
        // 添加排序
        query.orderBy(cb.desc(root.get<Instant>("createdAt")))
        
        val typedQuery = entityManager.createQuery(query)
        typedQuery.firstResult = pageable.offset.toInt()
        typedQuery.maxResults = pageable.pageSize
        
        val results = typedQuery.resultList
        val total = countRequirements(buyerId, category, status)
        
        return PageImpl(results, pageable, total)
    }
    
    // 使用原生SQL进行复杂查询优化
    @Query(
        value = """
            SELECT r.*, COUNT(b.id) as bid_count
            FROM requirement_mgmt.procurement_requirements r
            LEFT JOIN bidding_evaluation.bids b ON r.id = b.requirement_id
            WHERE r.status = 'PUBLISHED'
            GROUP BY r.id
            HAVING COUNT(b.id) < 5
            ORDER BY r.created_at DESC
            LIMIT :limit OFFSET :offset
        """,
        nativeQuery = true
    )
    fun findRequirementsWithLowBidCount(
        @Param("limit") limit: Int,
        @Param("offset") offset: Int
    ): List<ProcurementRequirement>
}
```

## 8. 新增Schema设计

### 8.1 用户洞察Schema (user_insights)
```sql
-- 用户洞察档案表
CREATE TABLE user_insights.user_insight_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    total_sessions INTEGER DEFAULT 0,
    average_session_duration INTERVAL DEFAULT '0 minutes',
    page_views INTEGER DEFAULT 0,
    click_through_rate DECIMAL(5,4) DEFAULT 0.0000,
    conversion_rate DECIMAL(5,4) DEFAULT 0.0000,
    last_active_time TIMESTAMP WITH TIME ZONE,
    industry VARCHAR(100),
    company_size VARCHAR(50),
    purchasing_power VARCHAR(50),
    preferred_categories TEXT[],
    risk_tolerance VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 行为模式表
CREATE TABLE user_insights.behavior_patterns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_insight_profile_id UUID REFERENCES user_insights.user_insight_profiles(id),
    pattern_type VARCHAR(100) NOT NULL,
    pattern_data JSONB NOT NULL,
    confidence_score DECIMAL(3,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 8.2 用户增长Schema (user_growth)
```sql
-- 用户增长策略表
CREATE TABLE user_growth.user_growth_strategies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    engagement_level INTEGER DEFAULT 0,
    engagement_score DECIMAL(10,2) DEFAULT 0.00,
    total_incentive_amount DECIMAL(15,2) DEFAULT 0.00,
    referral_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 推荐关系表
CREATE TABLE user_growth.referral_relations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    referrer_id UUID NOT NULL,
    referred_id UUID NOT NULL,
    incentive_type VARCHAR(50) NOT NULL,
    incentive_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'PENDING',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 8.3 智能推荐Schema (intelligent_recommendation)
```sql
-- 推荐引擎表
CREATE TABLE intelligent_recommendation.recommendation_engines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    engine_type VARCHAR(100) NOT NULL,
    algorithm_config JSONB NOT NULL,
    performance_metrics JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 推荐结果表
CREATE TABLE intelligent_recommendation.recommendation_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    engine_id UUID REFERENCES intelligent_recommendation.recommendation_engines(id),
    item_type VARCHAR(50) NOT NULL,
    item_id UUID NOT NULL,
    score DECIMAL(5,4) NOT NULL,
    reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 8.4 智能决策Schema (intelligent_decision)
```sql
-- 决策引擎表
CREATE TABLE intelligent_decision.decision_engines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    decision_type VARCHAR(100) NOT NULL,
    decision_rules JSONB NOT NULL,
    ml_models JSONB,
    performance_metrics JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 决策记录表
CREATE TABLE intelligent_decision.decision_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    engine_id UUID REFERENCES intelligent_decision.decision_engines(id),
    decision_context JSONB NOT NULL,
    decision_result JSONB NOT NULL,
    confidence_score DECIMAL(3,2),
    execution_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 8.5 合规管理Schema (compliance_management)
```sql
-- 合规策略表
CREATE TABLE compliance_management.compliance_policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    policy_name VARCHAR(200) NOT NULL,
    regulation_type VARCHAR(100) NOT NULL,
    compliance_rules JSONB NOT NULL,
    status VARCHAR(50) DEFAULT 'ACTIVE',
    effective_date DATE NOT NULL,
    expiry_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 审计追踪表
CREATE TABLE compliance_management.audit_trails (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    policy_id UUID REFERENCES compliance_management.compliance_policies(id),
    transaction_id UUID NOT NULL,
    check_result VARCHAR(50) NOT NULL,
    violations JSONB,
    checked_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 8.6 风险控制Schema (risk_control)
```sql
-- 风险评估模型表
CREATE TABLE risk_control.risk_assessment_models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    model_name VARCHAR(200) NOT NULL,
    risk_type VARCHAR(100) NOT NULL,
    risk_factors JSONB NOT NULL,
    risk_thresholds JSONB NOT NULL,
    model_performance JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 风险评估结果表
CREATE TABLE risk_control.risk_assessment_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    model_id UUID REFERENCES risk_control.risk_assessment_models(id),
    assessment_context JSONB NOT NULL,
    risk_score DECIMAL(5,2) NOT NULL,
    risk_level VARCHAR(50) NOT NULL,
    recommendations TEXT[],
    assessed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## 9. 架构优化总结

这套优化的全栈数据库设计为超级个人开发者提供了完整的数据存储方案，主要改进包括：

### 9.1 Schema结构优化
- **用户域重构**：将4个用户相关Schema合并为2个，提高数据一致性
- **智能域扩展**：将2个智能Schema扩展为3个，支持AI能力独立演进
- **治理域新增**：新增2个治理风控Schema，满足合规和风险控制需求

### 9.2 数据架构优势
- **边界清晰**：17个Schema保持限界上下文边界清晰
- **统一管理**：单一PostgreSQL实例简化运维
- **性能优化**：针对新架构优化的索引和查询策略
- **扩展性强**：支持未来的技术演进和业务发展

### 9.3 长远价值
- **架构演进**：支持从16上下文到17上下文的平滑迁移
- **技术前瞻**：为AI、大数据等新技术预留数据结构
- **监管适应**：完善的合规和审计数据支持

通过PostgreSQL的Schema分离保持了17个限界上下文的边界清晰，同时利用统一数据库的优势简化了运维和事务管理，为长远架构演进奠定了坚实的数据基础。
