# 超级个人全栈开发 - 技术架构文档

## 1. 超级个人技术架构总览

### 1.1 架构设计理念
- **全栈一体化**：前后端技术栈深度整合，减少上下文切换
- **开发效率优先**：选择能最大化个人开发效率的技术和工具
- **自动化驱动**：大量使用自动化工具和代码生成
- **企业级标准**：保持企业级系统的质量和性能标准
- **可扩展性**：架构支持后续团队扩展和功能增强

### 1.2 技术栈全景图

```mermaid
graph TB
    subgraph "开发工具层 (Development Tools)"
        IDE[IntelliJ IDEA Ultimate<br/>全栈开发IDE]
        GEN[代码生成工具<br/>JPA Buddy + OpenAPI Generator]
        BUILD[构建工具<br/>Gradle + Vite]
        VCS[版本控制<br/>Git + GitHub]
    end
    
    subgraph "前端技术栈 (Frontend Stack)"
        VUE[Vue 3<br/>现代化前端框架]
        TS[TypeScript<br/>类型安全]
        ELEMENT[Element Plus<br/>企业级UI]
        PINIA[Pinia<br/>状态管理]
        AXIOS[Axios<br/>数据获取]
        ROUTER[Vue Router<br/>路由管理]
        VUEUSE[VueUse<br/>组合式工具]
        CHART[ECharts<br/>数据可视化]
    end
    
    subgraph "后端技术栈 (Backend Stack)"
        KOTLIN[Kotlin 2.1<br/>现代JVM语言]
        SPRING[Spring Boot 3<br/>企业级框架]
        JPA[JPA + Hibernate<br/>ORM框架]
        SECURITY[Spring Security<br/>安全框架]
        EVENTS[Spring Events<br/>事件驱动]
        CACHE[Spring Cache<br/>缓存抽象]
        VALID[Bean Validation<br/>数据验证]
        OPENAPI[OpenAPI 3<br/>API文档]
    end
    
    subgraph "数据存储层 (Data Storage)"
        POSTGRES[(PostgreSQL 16<br/>主数据库)]
        REDIS[(Redis 7<br/>缓存数据库)]
        ELASTIC[(Elasticsearch<br/>搜索引擎)]
        MINIO[MinIO<br/>对象存储]
    end
    
    subgraph "基础设施层 (Infrastructure)"
        DOCKER[Docker<br/>容器化]
        COMPOSE[Docker Compose<br/>本地开发]
        GITHUB[GitHub Actions<br/>CI/CD]
        NGINX[Nginx<br/>反向代理]
    end
    
    subgraph "监控运维层 (Monitoring & Ops)"
        PROMETHEUS[Prometheus<br/>指标监控]
        GRAFANA[Grafana<br/>可视化]
        ELK[ELK Stack<br/>日志分析]
        SENTRY[Sentry<br/>错误追踪]
    end
    
    IDE --> VUE
    IDE --> KOTLIN
    GEN --> JPA
    GEN --> OPENAPI

    VUE --> ELEMENT
    VUE --> PINIA
    VUE --> ROUTER
    TS --> VUE
    PINIA --> AXIOS
    
    KOTLIN --> SPRING
    SPRING --> JPA
    SPRING --> SECURITY
    SPRING --> EVENTS
    
    JPA --> POSTGRES
    CACHE --> REDIS
    SPRING --> ELASTIC
    
    DOCKER --> POSTGRES
    DOCKER --> REDIS
    DOCKER --> ELASTIC
    
    GITHUB --> DOCKER
    PROMETHEUS --> GRAFANA
```

## 2. 后端技术架构深度设计

### 2.1 Spring Boot 3 + Kotlin 架构
```kotlin
// 主应用类
@SpringBootApplication
@EnableJpaRepositories(basePackages = ["com.procurement.platform.infrastructure.repository"])
@EnableCaching
@EnableScheduling
@EnableAsync
@EnableMethodSecurity
class ProcurementPlatformApplication

fun main(args: Array<String>) {
    runApplication<ProcurementPlatformApplication>(*args)
}

// 核心配置类
@Configuration
@EnableWebSecurity
class CoreConfiguration {
    
    @Bean
    fun passwordEncoder(): PasswordEncoder = BCryptPasswordEncoder()
    
    @Bean
    fun jwtDecoder(): JwtDecoder = NimbusJwtDecoder
        .withJwkSetUri("${auth.jwk-set-uri}")
        .build()
    
    @Bean
    fun objectMapper(): ObjectMapper = ObjectMapper().apply {
        registerModule(JavaTimeModule())
        registerModule(KotlinModule.Builder().build())
        disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
    }
    
    @Bean
    fun applicationEventMulticaster(): ApplicationEventMulticaster {
        val multicaster = SimpleApplicationEventMulticaster()
        multicaster.setTaskExecutor(ThreadPoolTaskExecutor().apply {
            corePoolSize = 4
            maxPoolSize = 8
            queueCapacity = 100
            setThreadNamePrefix("event-")
            initialize()
        })
        return multicaster
    }
}
```

### 2.2 DDD分层架构实现
```kotlin
// 应用服务层基类
@Transactional
abstract class ApplicationService {
    
    @Autowired
    protected lateinit var eventPublisher: DomainEventPublisher
    
    @Autowired
    protected lateinit var transactionTemplate: TransactionTemplate
    
    protected fun <T> executeInTransaction(action: () -> T): T {
        return transactionTemplate.execute { action() }!!
    }
    
    protected fun publishEvent(event: DomainEvent) {
        eventPublisher.publish(event)
    }
    
    protected fun publishEvents(events: List<DomainEvent>) {
        events.forEach { publishEvent(it) }
    }
}

// 仓储接口基类
interface Repository<T : AggregateRoot, ID : Any> {
    fun save(aggregate: T): T
    fun findById(id: ID): T?
    fun delete(aggregate: T)
    fun existsById(id: ID): Boolean
}

// JPA仓储实现基类
@Repository
abstract class JpaRepository<T : AggregateRoot, ID : Any, R : org.springframework.data.jpa.repository.JpaRepository<T, ID>>(
    protected val jpaRepository: R
) : Repository<T, ID> {
    
    override fun save(aggregate: T): T {
        val saved = jpaRepository.save(aggregate)
        // 发布聚合根中的领域事件
        publishDomainEvents(saved)
        return saved
    }
    
    override fun findById(id: ID): T? = jpaRepository.findById(id).orElse(null)
    
    override fun delete(aggregate: T) = jpaRepository.delete(aggregate)
    
    override fun existsById(id: ID): Boolean = jpaRepository.existsById(id)
    
    private fun publishDomainEvents(aggregate: T) {
        // 实现领域事件发布逻辑
    }
}
```

### 2.3 事件驱动架构
```kotlin
// 领域事件发布器
@Component
class DomainEventPublisher(
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val eventStore: EventStore
) {
    
    fun publish(event: DomainEvent) {
        // 持久化事件（可选）
        eventStore.store(event)
        
        // 发布事件
        applicationEventPublisher.publishEvent(event)
        
        // 记录指标
        Metrics.counter("domain.events.published", 
            Tags.of("type", event::class.simpleName!!)).increment()
    }
    
    fun publishAll(events: List<DomainEvent>) {
        events.forEach { publish(it) }
    }
}

// 事件存储
@Component
class EventStore(
    private val eventRepository: EventRepository
) {
    
    fun store(event: DomainEvent) {
        val eventRecord = EventRecord(
            eventId = event.eventId,
            eventType = event::class.qualifiedName!!,
            aggregateId = event.aggregateId,
            aggregateType = event.aggregateType,
            eventData = objectMapper.writeValueAsString(event),
            version = event.version,
            occurredOn = event.occurredOn
        )
        eventRepository.save(eventRecord)
    }
    
    fun getEvents(aggregateId: String): List<DomainEvent> {
        return eventRepository.findByAggregateIdOrderByVersion(aggregateId)
            .map { deserializeEvent(it) }
    }
}
```

## 3. 前端技术架构深度设计

### 3.1 Vue 3 + TypeScript 架构
```typescript
// 项目结构
src/
├── components/              # 通用组件
│   ├── common/             # 基础组件
│   ├── business/           # 业务组件
│   └── layout/             # 布局组件
├── pages/                  # 页面组件
│   ├── requirements/       # 需求管理页面
│   ├── suppliers/          # 供应商管理页面
│   ├── orders/             # 订单管理页面
│   └── analytics/          # 数据分析页面
├── composables/            # 组合式函数
├── services/               # API服务
├── stores/                 # Pinia状态管理
├── router/                 # Vue Router配置
├── types/                  # TypeScript类型定义
├── utils/                  # 工具函数
├── constants/              # 常量定义
└── styles/                 # 样式文件

// 主应用组件 - main.ts
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import 'element-plus/dist/index.css'
import App from './App.vue'
import { routes } from './router'
import { useAuthStore } from './stores/auth'

const app = createApp(App)
const pinia = createPinia()
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else {
    next()
  }
})

app.use(pinia)
app.use(router)
app.use(ElementPlus, { locale: zhCn })
app.mount('#app')
```

### 3.2 状态管理架构
```typescript
// Pinia 配置
import { createPinia } from 'pinia';
import { createPersistedState } from 'pinia-plugin-persistedstate';

const pinia = createPinia();
pinia.use(createPersistedState());

export default pinia;

// 认证状态管理
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null);
  const token = ref(localStorage.getItem('token'));
  const isLoading = ref(false);

  const isAuthenticated = computed(() => !!user.value && !!token.value);

  async function login(credentials) {
    isLoading.value = true;
    try {
      const response = await authApi.login(credentials);
      user.value = response.user;
      token.value = response.token;
      localStorage.setItem('token', response.token);
    } finally {
      isLoading.value = false;
    }
  }

  function logout() {
    user.value = null;
    token.value = null;
    localStorage.removeItem('token');
  }

  return { user, token, isLoading, isAuthenticated, login, logout };
}, {
  persist: {
    key: 'auth',
    storage: localStorage,
    paths: ['user', 'token']
  }
});

// API 服务定义
import axios from 'axios';
import { useAuthStore } from '@/stores/auth';

const apiClient = axios.create({
  baseURL: '/api',
  timeout: 10000,
});

// 请求拦截器
apiClient.interceptors.request.use((config) => {
  const authStore = useAuthStore();
  if (authStore.token) {
    config.headers.Authorization = `Bearer ${authStore.token}`;
  }
  return config;
});

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401) {
      const authStore = useAuthStore();
      authStore.logout();
    }
    return Promise.reject(error);
  }
);

// 需求管理API服务
export const requirementService = {
  async getRequirements(params: RequirementQuery): Promise<PaginatedResponse<Requirement>> {
    return apiClient.get('/requirements', { params });
  },

  async createRequirement(data: CreateRequirementRequest): Promise<Requirement> {
    return apiClient.post('/requirements', data);
  },

  async publishRequirement(id: string): Promise<Requirement> {
    return apiClient.post(`/requirements/${id}/publish`);
  },

  async updateRequirement(id: string, data: UpdateRequirementRequest): Promise<Requirement> {
    return apiClient.put(`/requirements/${id}`, data);
  }
};
```

### 3.3 组件架构设计
```vue
<!-- 业务组件示例：需求管理 -->
<template>
  <el-card class="requirement-list">
    <template #header>
      <div class="card-header">
        <span>需求管理</span>
        <el-button type="primary" @click="onCreate">
          <el-icon><Plus /></el-icon>
          创建需求
        </el-button>
      </div>
    </template>

    <el-table
      :data="requirements"
      :loading="loading"
      v-loading="loading"
    >
      <el-table-column prop="title" label="需求标题" show-overflow-tooltip />

      <el-table-column prop="category" label="分类" width="120">
        <template #default="{ row }">
          <el-tag>{{ row.category }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="budget" label="预算" width="150">
        <template #default="{ row }">
          {{ formatBudget(row.budget) }}
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="onView(row)">查看</el-button>
          <el-button size="small" type="primary" @click="onEdit(row)">编辑</el-button>
          <el-button
            v-if="row.status === 'DRAFT'"
            size="small"
            type="success"
            @click="handlePublish(row.id)"
          >
            发布
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="page"
      v-model:page-size="pageSize"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useRequirementStore } from '@/stores/requirement'
import { formatCurrency } from '@/utils/format'
import type { Requirement } from '@/types/requirement'

interface Props {
  // 可以添加props
}

interface Emits {
  (e: 'edit', requirement: Requirement): void
  (e: 'view', requirement: Requirement): void
  (e: 'create'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const requirementStore = useRequirementStore()
const page = ref(1)
const pageSize = ref(20)
const loading = ref(false)
const requirements = ref<Requirement[]>([])
const total = ref(0)

const handlePublish = async (id: string) => {
  try {
    await requirementStore.publishRequirement(id)
    ElMessage.success('需求发布成功')
    await fetchRequirements()
  } catch (error) {
    ElMessage.error('需求发布失败')
  }
}
  
  const columns = [
    {
      title: '需求标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      render: (category: string) => <Tag>{category}</Tag>,
    },
    {
      title: '预算',
      dataIndex: 'budget',
      key: 'budget',
      render: (budget: Budget) => formatCurrency(budget),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: RequirementStatus) => (
        <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: Requirement) => (
        <Space>
          <el-button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => onView(record)}
          >
            查看
          </Button>
          {record.status === RequirementStatus.DRAFT && (
            <>
              <el-button
                type="link"
                icon={<EditOutlined />}
                onClick={() => onEdit(record)}
              >
                编辑
              </Button>
              <el-button
                type="link"
                onClick={() => handlePublish(record.id)}
              >
                发布
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];
  
  return (
    <el-card
      title="采购需求管理"
      extra={
        <el-button
          type="primary"
          icon={<PlusOutlined />}
          onClick={onCreate}
        >
          创建需求
        </Button>
      }
    >
      <el-table
        columns={columns}
        dataSource={data?.content}
        loading={isLoading}
        pagination={{
          current: page,
          pageSize,
          total: data?.totalElements,
          showSizeChanger: true,
          showQuickJumper: true,
          onChange: setPage,
          onShowSizeChange: (_, size) => setPageSize(size),
        }}
        rowKey="id"
      />
    </Card>
  );
};
```

## 4. 数据库架构设计

### 4.1 PostgreSQL 统一数据库
```sql
-- 数据库配置
-- postgresql.conf 优化配置
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200

-- 连接池配置
max_connections = 200
```

### 4.2 Schema分离设计
```sql
-- 创建各个限界上下文的Schema
CREATE SCHEMA requirement_mgmt;
CREATE SCHEMA supplier_discovery;
CREATE SCHEMA bidding_evaluation;
CREATE SCHEMA order_fulfillment;
CREATE SCHEMA logistics_service;
CREATE SCHEMA payment_settlement;
CREATE SCHEMA inventory_operations;
CREATE SCHEMA supplier_relationship;
CREATE SCHEMA intelligent_replenishment;
CREATE SCHEMA user_behavior;
CREATE SCHEMA user_profile;
CREATE SCHEMA user_engagement;
CREATE SCHEMA incentive_growth;
CREATE SCHEMA business_analytics;
CREATE SCHEMA intelligent_operations;
CREATE SCHEMA identity_access;
CREATE SCHEMA communication;

-- 共享Schema
CREATE SCHEMA shared;

-- 事件存储Schema
CREATE SCHEMA event_store;
```

### 4.3 JPA配置优化
```kotlin
// JPA配置
@Configuration
@EnableJpaRepositories(
    basePackages = ["com.procurement.platform.infrastructure.repository"],
    repositoryImplementationPostfix = "Impl"
)
class JpaConfiguration {
    
    @Bean
    @Primary
    fun dataSource(): DataSource {
        return HikariDataSource(HikariConfig().apply {
            jdbcUrl = "*****************************************************"
            username = "procurement_user"
            password = "procurement_password"
            driverClassName = "org.postgresql.Driver"
            
            // 连接池配置
            maximumPoolSize = 20
            minimumIdle = 5
            connectionTimeout = 30000
            idleTimeout = 600000
            maxLifetime = 1800000
            leakDetectionThreshold = 60000
            
            // 性能优化
            addDataSourceProperty("cachePrepStmts", "true")
            addDataSourceProperty("prepStmtCacheSize", "250")
            addDataSourceProperty("prepStmtCacheSqlLimit", "2048")
            addDataSourceProperty("useServerPrepStmts", "true")
        })
    }
    
    @Bean
    fun jpaProperties(): Properties {
        return Properties().apply {
            // Hibernate配置
            setProperty("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect")
            setProperty("hibernate.hbm2ddl.auto", "validate")
            setProperty("hibernate.show_sql", "false")
            setProperty("hibernate.format_sql", "true")
            
            // 性能优化
            setProperty("hibernate.jdbc.batch_size", "25")
            setProperty("hibernate.order_inserts", "true")
            setProperty("hibernate.order_updates", "true")
            setProperty("hibernate.jdbc.batch_versioned_data", "true")
            
            // 二级缓存
            setProperty("hibernate.cache.use_second_level_cache", "true")
            setProperty("hibernate.cache.use_query_cache", "true")
            setProperty("hibernate.cache.region.factory_class", 
                "org.hibernate.cache.jcache.JCacheRegionFactory")
        }
    }
}
```

## 5. 开发工具链优化

### 5.1 IntelliJ IDEA 配置
```kotlin
// .editorconfig
root = true

[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

[*.{kt,kts}]
indent_style = space
indent_size = 4
max_line_length = 120

[*.{js,ts,tsx,json}]
indent_style = space
indent_size = 2

// ktlint配置
// build.gradle.kts
plugins {
    id("org.jlleitschuh.gradle.ktlint") version "11.6.1"
}

ktlint {
    version.set("0.50.0")
    debug.set(false)
    verbose.set(true)
    android.set(false)
    outputToConsole.set(true)
    outputColorName.set("RED")
    ignoreFailures.set(false)
}
```

### 5.2 自动化工具配置
```yaml
# GitHub Actions CI/CD
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
    
    - name: Run backend tests
      run: ./gradlew test
    
    - name: Run frontend tests
      run: |
        cd frontend
        npm ci
        npm run test:ci
    
    - name: Build application
      run: |
        ./gradlew build
        cd frontend
        npm run build
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
```

这套超级个人技术架构为全栈开发者提供了完整的技术基础，通过现代化的技术栈和高度自动化的工具链，使个人开发者能够高效构建和维护企业级系统。
