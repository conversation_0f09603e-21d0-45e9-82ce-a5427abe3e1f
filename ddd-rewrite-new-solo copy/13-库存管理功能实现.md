# 超级个人全栈开发 - 库存管理功能实现文档

## 1. 库存管理系统总览（完全保持原有设计）

### 1.1 智能库存管理架构
- **实时库存跟踪**：多仓库、多SKU的实时库存监控
- **智能预警系统**：基于消耗模式的动态预警
- **自动补货建议**：基于信任关系的智能补货
- **库存优化算法**：ABC分类、安全库存、最优订货量
- **多维度分析**：库存周转、呆滞分析、成本优化

### 1.2 库存管理架构图
```mermaid
graph TB
    subgraph "数据采集层 (Data Collection)"
        ORDER_DATA[订单数据<br/>入库/出库/调拨]
        SUPPLIER_DATA[供应商数据<br/>交期/价格/质量]
        CONSUMPTION_DATA[消耗数据<br/>使用量/频次/趋势]
        EXTERNAL_DATA[外部数据<br/>市场价格/季节性]
    end
    
    subgraph "库存核心层 (Inventory Core)"
        REALTIME_INVENTORY[实时库存<br/>当前库存/在途库存]
        WAREHOUSE_MGMT[仓库管理<br/>多仓库/货位管理]
        SKU_MGMT[SKU管理<br/>商品主数据/规格]
        BATCH_TRACKING[批次跟踪<br/>批次号/有效期]
    end
    
    subgraph "分析引擎层 (Analytics Engine)"
        CONSUMPTION_ANALYSIS[消耗分析<br/>历史消耗/趋势预测]
        ABC_ANALYSIS[ABC分析<br/>重要性分类]
        TURNOVER_ANALYSIS[周转分析<br/>周转率/滞销分析]
        SAFETY_CALC[安全库存<br/>安全库存计算]
    end
    
    subgraph "智能决策层 (Intelligent Decision)"
        DEMAND_FORECAST[需求预测<br/>机器学习预测]
        REORDER_CALC[补货计算<br/>最优订货量/时机]
        SUPPLIER_SELECT[供应商选择<br/>基于信任关系]
        RISK_ASSESSMENT[风险评估<br/>缺货/过期风险]
    end
    
    subgraph "预警系统层 (Alert System)"
        LOW_STOCK_ALERT[低库存预警<br/>动态阈值预警]
        EXPIRY_ALERT[过期预警<br/>临期商品提醒]
        SLOW_MOVING_ALERT[滞销预警<br/>呆滞库存识别]
        COST_ALERT[成本预警<br/>库存成本异常]
    end
    
    subgraph "应用服务层 (Application Services)"
        INVENTORY_QUERY[库存查询<br/>实时库存查询]
        REPLENISHMENT[智能补货<br/>一键补货执行]
        OPTIMIZATION[库存优化<br/>库存结构优化]
        REPORTING[报表分析<br/>库存报表生成]
    end
    
    ORDER_DATA --> REALTIME_INVENTORY
    SUPPLIER_DATA --> REALTIME_INVENTORY
    CONSUMPTION_DATA --> CONSUMPTION_ANALYSIS
    EXTERNAL_DATA --> DEMAND_FORECAST
    
    REALTIME_INVENTORY --> CONSUMPTION_ANALYSIS
    WAREHOUSE_MGMT --> ABC_ANALYSIS
    SKU_MGMT --> TURNOVER_ANALYSIS
    BATCH_TRACKING --> SAFETY_CALC
    
    CONSUMPTION_ANALYSIS --> DEMAND_FORECAST
    ABC_ANALYSIS --> REORDER_CALC
    TURNOVER_ANALYSIS --> SUPPLIER_SELECT
    SAFETY_CALC --> RISK_ASSESSMENT
    
    DEMAND_FORECAST --> LOW_STOCK_ALERT
    REORDER_CALC --> EXPIRY_ALERT
    SUPPLIER_SELECT --> SLOW_MOVING_ALERT
    RISK_ASSESSMENT --> COST_ALERT
    
    LOW_STOCK_ALERT --> INVENTORY_QUERY
    EXPIRY_ALERT --> REPLENISHMENT
    SLOW_MOVING_ALERT --> OPTIMIZATION
    COST_ALERT --> REPORTING
```

## 2. 核心库存管理实现

### 2.1 实时库存跟踪
```kotlin
// 库存聚合根
@Entity
@Table(schema = "inventory_operations", name = "inventory_items")
class InventoryItem(
    @EmbeddedId val id: InventoryItemId,
    val buyerId: UserId,
    val warehouseId: WarehouseId,
    val productCode: String,
    val productName: String,
    @Embedded var currentStock: Stock,
    @Embedded var reservedStock: Stock,
    @Embedded var inTransitStock: Stock,
    @Embedded var safetyStock: Stock,
    @Embedded var reorderPoint: Stock,
    @Embedded var maxStockLevel: Stock,
    @Embedded var unitCost: Money,
    var lastUpdated: Instant,
    var version: Int = 1,
    
    @OneToMany(mappedBy = "inventoryItem", cascade = [CascadeType.ALL])
    private val _stockMovements: MutableList<StockMovement> = mutableListOf(),
    
    @OneToMany(mappedBy = "inventoryItem", cascade = [CascadeType.ALL])
    private val _batches: MutableList<InventoryBatch> = mutableListOf()
) {
    val stockMovements: List<StockMovement> get() = _stockMovements.toList()
    val batches: List<InventoryBatch> get() = _batches.toList()
    
    // 可用库存
    val availableStock: Stock
        get() = Stock(currentStock.quantity - reservedStock.quantity, currentStock.unit)
    
    // 总库存（包含在途）
    val totalStock: Stock
        get() = Stock(currentStock.quantity + inTransitStock.quantity, currentStock.unit)
    
    // 入库操作
    fun receiveStock(
        quantity: Int,
        unitCost: Money,
        batchNumber: String?,
        expiryDate: LocalDate?,
        orderId: OrderId?
    ): StockReceivedEvent {
        require(quantity > 0) { "入库数量必须大于0" }
        
        // 更新当前库存
        currentStock = Stock(currentStock.quantity + quantity, currentStock.unit)
        
        // 减少在途库存（如果是订单入库）
        if (orderId != null && inTransitStock.quantity >= quantity) {
            inTransitStock = Stock(inTransitStock.quantity - quantity, inTransitStock.unit)
        }
        
        // 记录库存变动
        val movement = StockMovement(
            id = StockMovementId.generate(),
            inventoryItem = this,
            movementType = StockMovementType.INBOUND,
            quantity = quantity,
            unitCost = unitCost,
            orderId = orderId,
            batchNumber = batchNumber,
            occurredAt = Instant.now()
        )
        _stockMovements.add(movement)
        
        // 创建批次记录
        if (batchNumber != null) {
            val batch = InventoryBatch(
                id = InventoryBatchId.generate(),
                inventoryItem = this,
                batchNumber = batchNumber,
                quantity = quantity,
                unitCost = unitCost,
                expiryDate = expiryDate,
                createdAt = Instant.now()
            )
            _batches.add(batch)
        }
        
        lastUpdated = Instant.now()
        
        return StockReceivedEvent(
            inventoryItemId = id.value,
            buyerId = buyerId.value,
            productCode = productCode,
            quantity = quantity,
            newStockLevel = currentStock.quantity,
            orderId = orderId?.value
        )
    }
    
    // 出库操作
    fun issueStock(
        quantity: Int,
        orderId: OrderId?,
        reason: String = "正常出库"
    ): StockIssuedEvent {
        require(quantity > 0) { "出库数量必须大于0" }
        require(availableStock.quantity >= quantity) { "可用库存不足" }
        
        // 更新当前库存
        currentStock = Stock(currentStock.quantity - quantity, currentStock.unit)
        
        // 记录库存变动
        val movement = StockMovement(
            id = StockMovementId.generate(),
            inventoryItem = this,
            movementType = StockMovementType.OUTBOUND,
            quantity = -quantity,
            unitCost = unitCost,
            orderId = orderId,
            reason = reason,
            occurredAt = Instant.now()
        )
        _stockMovements.add(movement)
        
        // 更新批次库存（FIFO原则）
        updateBatchesForOutbound(quantity)
        
        lastUpdated = Instant.now()
        
        return StockIssuedEvent(
            inventoryItemId = id.value,
            buyerId = buyerId.value,
            productCode = productCode,
            quantity = quantity,
            newStockLevel = currentStock.quantity,
            orderId = orderId?.value
        )
    }
    
    // 预留库存
    fun reserveStock(quantity: Int, orderId: OrderId): StockReservedEvent {
        require(quantity > 0) { "预留数量必须大于0" }
        require(availableStock.quantity >= quantity) { "可用库存不足" }
        
        reservedStock = Stock(reservedStock.quantity + quantity, reservedStock.unit)
        lastUpdated = Instant.now()
        
        return StockReservedEvent(
            inventoryItemId = id.value,
            buyerId = buyerId.value,
            productCode = productCode,
            quantity = quantity,
            orderId = orderId.value
        )
    }
    
    // 释放预留库存
    fun releaseReservedStock(quantity: Int, orderId: OrderId): StockReservationReleasedEvent {
        require(quantity > 0) { "释放数量必须大于0" }
        require(reservedStock.quantity >= quantity) { "预留库存不足" }
        
        reservedStock = Stock(reservedStock.quantity - quantity, reservedStock.unit)
        lastUpdated = Instant.now()
        
        return StockReservationReleasedEvent(
            inventoryItemId = id.value,
            buyerId = buyerId.value,
            productCode = productCode,
            quantity = quantity,
            orderId = orderId.value
        )
    }
    
    // 检查是否需要补货
    fun needsReplenishment(): Boolean {
        return availableStock.quantity <= reorderPoint.quantity
    }
    
    // 检查是否库存过多
    fun isOverstocked(): Boolean {
        return currentStock.quantity > maxStockLevel.quantity
    }
    
    // 计算库存周转率
    fun calculateTurnoverRate(days: Int = 365): Double {
        val totalOutbound = _stockMovements
            .filter { it.movementType == StockMovementType.OUTBOUND }
            .filter { it.occurredAt.isAfter(Instant.now().minus(Duration.ofDays(days.toLong()))) }
            .sumOf { abs(it.quantity) }
        
        val averageStock = (currentStock.quantity + totalOutbound) / 2.0
        
        return if (averageStock > 0) totalOutbound / averageStock else 0.0
    }
    
    // 更新批次库存（FIFO）
    private fun updateBatchesForOutbound(quantity: Int) {
        var remainingQuantity = quantity
        val sortedBatches = _batches
            .filter { it.remainingQuantity > 0 }
            .sortedBy { it.createdAt }
        
        for (batch in sortedBatches) {
            if (remainingQuantity <= 0) break
            
            val issueFromBatch = minOf(remainingQuantity, batch.remainingQuantity)
            batch.issueQuantity(issueFromBatch)
            remainingQuantity -= issueFromBatch
        }
    }
}

// 库存批次实体
@Entity
@Table(schema = "inventory_operations", name = "inventory_batches")
class InventoryBatch(
    @EmbeddedId val id: InventoryBatchId,
    @ManyToOne val inventoryItem: InventoryItem,
    val batchNumber: String,
    val originalQuantity: Int,
    var remainingQuantity: Int,
    @Embedded val unitCost: Money,
    val expiryDate: LocalDate?,
    val createdAt: Instant
) {
    init {
        remainingQuantity = originalQuantity
    }
    
    fun issueQuantity(quantity: Int) {
        require(quantity > 0) { "出库数量必须大于0" }
        require(remainingQuantity >= quantity) { "批次库存不足" }
        
        remainingQuantity -= quantity
    }
    
    fun isExpired(): Boolean {
        return expiryDate?.isBefore(LocalDate.now()) ?: false
    }
    
    fun isNearExpiry(days: Int = 30): Boolean {
        return expiryDate?.isBefore(LocalDate.now().plusDays(days.toLong())) ?: false
    }
}
```

### 2.2 智能预警系统
```kotlin
// 库存预警服务
@Service
class InventoryAlertService(
    private val inventoryRepository: InventoryRepository,
    private val alertRepository: AlertRepository,
    private val notificationService: NotificationService,
    private val demandForecastService: DemandForecastService
) {
    
    @Scheduled(fixedRate = 300000) // 每5分钟检查一次
    fun checkInventoryAlerts() {
        val allInventoryItems = inventoryRepository.findAll()
        
        allInventoryItems.forEach { item ->
            checkLowStockAlert(item)
            checkExpiryAlert(item)
            checkSlowMovingAlert(item)
            checkOverstockAlert(item)
        }
    }
    
    // 低库存预警
    private fun checkLowStockAlert(item: InventoryItem) {
        if (item.needsReplenishment()) {
            val existingAlert = alertRepository.findActiveAlert(
                item.id, AlertType.LOW_STOCK
            )
            
            if (existingAlert == null) {
                val alert = InventoryAlert(
                    id = AlertId.generate(),
                    inventoryItemId = item.id,
                    alertType = AlertType.LOW_STOCK,
                    severity = calculateLowStockSeverity(item),
                    title = "库存不足预警",
                    message = "商品 ${item.productName} 库存已降至 ${item.availableStock.quantity}，" +
                            "低于安全库存 ${item.safetyStock.quantity}",
                    data = mapOf(
                        "currentStock" to item.currentStock.quantity,
                        "safetyStock" to item.safetyStock.quantity,
                        "reorderPoint" to item.reorderPoint.quantity,
                        "suggestedOrderQuantity" to calculateSuggestedOrderQuantity(item)
                    ),
                    createdAt = Instant.now()
                )
                
                alertRepository.save(alert)
                notificationService.sendAlert(alert)
            }
        }
    }
    
    // 过期预警
    private fun checkExpiryAlert(item: InventoryItem) {
        val nearExpiryBatches = item.batches.filter { 
            it.isNearExpiry(30) && it.remainingQuantity > 0 
        }
        
        if (nearExpiryBatches.isNotEmpty()) {
            val existingAlert = alertRepository.findActiveAlert(
                item.id, AlertType.NEAR_EXPIRY
            )
            
            if (existingAlert == null) {
                val totalNearExpiryQuantity = nearExpiryBatches.sumOf { it.remainingQuantity }
                
                val alert = InventoryAlert(
                    id = AlertId.generate(),
                    inventoryItemId = item.id,
                    alertType = AlertType.NEAR_EXPIRY,
                    severity = AlertSeverity.MEDIUM,
                    title = "商品临期预警",
                    message = "商品 ${item.productName} 有 $totalNearExpiryQuantity 件即将过期",
                    data = mapOf(
                        "nearExpiryBatches" to nearExpiryBatches.map {
                            mapOf(
                                "batchNumber" to it.batchNumber,
                                "quantity" to it.remainingQuantity,
                                "expiryDate" to it.expiryDate
                            )
                        }
                    ),
                    createdAt = Instant.now()
                )
                
                alertRepository.save(alert)
                notificationService.sendAlert(alert)
            }
        }
    }
    
    // 滞销预警
    private fun checkSlowMovingAlert(item: InventoryItem) {
        val turnoverRate = item.calculateTurnoverRate(90) // 90天周转率
        
        if (turnoverRate < 0.5 && item.currentStock.quantity > 0) { // 周转率低于0.5
            val existingAlert = alertRepository.findActiveAlert(
                item.id, AlertType.SLOW_MOVING
            )
            
            if (existingAlert == null) {
                val alert = InventoryAlert(
                    id = AlertId.generate(),
                    inventoryItemId = item.id,
                    alertType = AlertType.SLOW_MOVING,
                    severity = AlertSeverity.LOW,
                    title = "滞销商品预警",
                    message = "商品 ${item.productName} 90天周转率仅为 ${String.format("%.2f", turnoverRate)}，" +
                            "建议关注销售策略",
                    data = mapOf(
                        "turnoverRate" to turnoverRate,
                        "currentStock" to item.currentStock.quantity,
                        "daysOfStock" to calculateDaysOfStock(item)
                    ),
                    createdAt = Instant.now()
                )
                
                alertRepository.save(alert)
                notificationService.sendAlert(alert)
            }
        }
    }
    
    // 超储预警
    private fun checkOverstockAlert(item: InventoryItem) {
        if (item.isOverstocked()) {
            val existingAlert = alertRepository.findActiveAlert(
                item.id, AlertType.OVERSTOCK
            )
            
            if (existingAlert == null) {
                val alert = InventoryAlert(
                    id = AlertId.generate(),
                    inventoryItemId = item.id,
                    alertType = AlertType.OVERSTOCK,
                    severity = AlertSeverity.MEDIUM,
                    title = "库存超储预警",
                    message = "商品 ${item.productName} 当前库存 ${item.currentStock.quantity} " +
                            "超过最大库存水平 ${item.maxStockLevel.quantity}",
                    data = mapOf(
                        "currentStock" to item.currentStock.quantity,
                        "maxStockLevel" to item.maxStockLevel.quantity,
                        "excessQuantity" to (item.currentStock.quantity - item.maxStockLevel.quantity)
                    ),
                    createdAt = Instant.now()
                )
                
                alertRepository.save(alert)
                notificationService.sendAlert(alert)
            }
        }
    }
    
    // 计算低库存严重程度
    private fun calculateLowStockSeverity(item: InventoryItem): AlertSeverity {
        val stockRatio = item.availableStock.quantity.toDouble() / item.safetyStock.quantity
        
        return when {
            stockRatio <= 0.2 -> AlertSeverity.CRITICAL
            stockRatio <= 0.5 -> AlertSeverity.HIGH
            stockRatio <= 0.8 -> AlertSeverity.MEDIUM
            else -> AlertSeverity.LOW
        }
    }
    
    // 计算建议订货量
    private fun calculateSuggestedOrderQuantity(item: InventoryItem): Int {
        val forecast = demandForecastService.forecastDemand(item.id, 30)
        val currentStock = item.availableStock.quantity
        val safetyStock = item.safetyStock.quantity
        
        return maxOf(0, forecast + safetyStock - currentStock)
    }
    
    // 计算库存天数
    private fun calculateDaysOfStock(item: InventoryItem): Int {
        val dailyConsumption = demandForecastService.calculateDailyConsumption(item.id)
        
        return if (dailyConsumption > 0) {
            (item.currentStock.quantity / dailyConsumption).toInt()
        } else {
            Int.MAX_VALUE
        }
    }
}

// 需求预测服务
@Service
class DemandForecastService(
    private val stockMovementRepository: StockMovementRepository,
    private val orderRepository: OrderRepository
) {
    
    // 预测未来需求
    fun forecastDemand(inventoryItemId: InventoryItemId, days: Int): Int {
        val historicalData = getHistoricalConsumption(inventoryItemId, 90)
        
        if (historicalData.isEmpty()) {
            return 0
        }
        
        // 简单移动平均预测
        val averageDailyConsumption = historicalData.values.average()
        
        // 考虑趋势和季节性（简化版）
        val trendFactor = calculateTrendFactor(historicalData)
        val seasonalFactor = calculateSeasonalFactor(inventoryItemId)
        
        val forecastDailyConsumption = averageDailyConsumption * trendFactor * seasonalFactor
        
        return (forecastDailyConsumption * days).toInt()
    }
    
    // 计算日均消耗
    fun calculateDailyConsumption(inventoryItemId: InventoryItemId): Double {
        val historicalData = getHistoricalConsumption(inventoryItemId, 30)
        
        return if (historicalData.isNotEmpty()) {
            historicalData.values.average()
        } else {
            0.0
        }
    }
    
    // 获取历史消耗数据
    private fun getHistoricalConsumption(
        inventoryItemId: InventoryItemId, 
        days: Int
    ): Map<LocalDate, Double> {
        val startDate = LocalDate.now().minusDays(days.toLong())
        
        val movements = stockMovementRepository.findOutboundMovements(
            inventoryItemId, startDate.atStartOfDay().toInstant(ZoneOffset.UTC)
        )
        
        return movements.groupBy { 
            it.occurredAt.atZone(ZoneOffset.UTC).toLocalDate() 
        }.mapValues { (_, dayMovements) ->
            dayMovements.sumOf { abs(it.quantity) }.toDouble()
        }
    }
    
    // 计算趋势因子
    private fun calculateTrendFactor(historicalData: Map<LocalDate, Double>): Double {
        if (historicalData.size < 7) return 1.0
        
        val sortedData = historicalData.toList().sortedBy { it.first }
        val firstWeekAvg = sortedData.take(7).map { it.second }.average()
        val lastWeekAvg = sortedData.takeLast(7).map { it.second }.average()
        
        return if (firstWeekAvg > 0) {
            (lastWeekAvg / firstWeekAvg).coerceIn(0.5, 2.0)
        } else {
            1.0
        }
    }
    
    // 计算季节性因子
    private fun calculateSeasonalFactor(inventoryItemId: InventoryItemId): Double {
        // 简化版：根据月份调整
        val currentMonth = LocalDate.now().monthValue
        
        return when (currentMonth) {
            12, 1, 2 -> 1.2  // 冬季需求增加
            6, 7, 8 -> 0.9   // 夏季需求减少
            else -> 1.0      // 其他月份正常
        }
    }
}
```

## 3. 前端库存管理界面

### 3.1 库存仪表板
```typescript
// 库存仪表板组件
export const InventoryDashboard: Vue Component = () => {
  const { data: inventorySummary, isLoading } = useGetInventorySummaryQuery();
  const { data: alerts } = useGetInventoryAlertsQuery();
  const { data: topItems } = useGetTopInventoryItemsQuery();
  
  if (isLoading) {
    return <InventoryDashboardSkeleton />;
  }
  
  return (
    <div class="inventory-dashboard">
      {/* 库存概览 */}
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <el-card>
            <Statistic
              title="总SKU数"
              value={inventorySummary?.totalSkus || 0}
              prefix={<AppstoreOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <el-card>
            <Statistic
              title="库存总值"
              value={inventorySummary?.totalValue || 0}
              prefix="¥"
              formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            />
          </Card>
        </Col>
        <Col span={6}>
          <el-card>
            <Statistic
              title="低库存商品"
              value={inventorySummary?.lowStockItems || 0}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <el-card>
            <Statistic
              title="平均周转率"
              value={inventorySummary?.averageTurnoverRate || 0}
              precision={2}
              suffix="次/年"
              prefix={<SyncOutlined />}
            />
          </Card>
        </Col>
      </Row>
      
      {/* 预警信息 */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={24}>
          <el-card title="库存预警" extra={<el-button type="link">查看全部</Button>}>
            <InventoryAlertsDisplay alerts={alerts || []} />
          </Card>
        </Col>
      </Row>
      
      {/* 库存分析 */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={12}>
          <el-card title="库存分布">
            <InventoryDistributionChart data={inventorySummary?.distribution} />
          </Card>
        </Col>
        <Col span={12}>
          <el-card title="周转率分析">
            <TurnoverAnalysisChart data={inventorySummary?.turnoverAnalysis} />
          </Card>
        </Col>
      </Row>
      
      {/* 热门商品 */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={24}>
          <el-card title="库存热门商品">
            <TopInventoryItemsTable data={topItems || []} />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

// 库存预警展示组件
const InventoryAlertsDisplay: Vue Component<{ alerts: InventoryAlert[] }> = ({ alerts }) => {
  const getAlertIcon = (type: AlertType) => {
    switch (type) {
      case 'LOW_STOCK': return <ExclamationCircleOutlined />;
      case 'NEAR_EXPIRY': return <ClockCircleOutlined />;
      case 'SLOW_MOVING': return <FallOutlined />;
      case 'OVERSTOCK': return <RiseOutlined />;
      default: return <InfoCircleOutlined />;
    }
  };
  
  const getAlertColor = (severity: AlertSeverity) => {
    switch (severity) {
      case 'CRITICAL': return '#ff4d4f';
      case 'HIGH': return '#fa8c16';
      case 'MEDIUM': return '#faad14';
      case 'LOW': return '#52c41a';
      default: return '#1890ff';
    }
  };
  
  return (
    <List
      dataSource={alerts.slice(0, 5)}
      renderItem={(alert) => (
        <List.Item
          actions={[
            <el-button key="handle" type="link" size="small">
              处理
            </Button>
          ]}
        >
          <List.Item.Meta
            avatar={
              <Avatar 
                icon={getAlertIcon(alert.alertType)} 
                style={{ backgroundColor: getAlertColor(alert.severity) }}
              />
            }
            title={alert.title}
            description={
              <div>
                <div>{alert.message}</div>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {formatDistanceToNow(new Date(alert.createdAt), { addSuffix: true, locale: zhCN })}
                </Text>
              </div>
            }
          />
        </List.Item>
      )}
    />
  );
};

// 智能补货组件
export const IntelligentReplenishmentPanel: Vue Component<{ inventoryItemId: string }> = ({ 
  inventoryItemId 
}) => {
  const { data: item } = useGetInventoryItemQuery(inventoryItemId);
  const { data: recommendation } = useGetReplenishmentRecommendationQuery(inventoryItemId);
  const [executeReplenishment] = useExecuteReplenishmentMutation();
  
  const handleExecuteReplenishment = async () => {
    try {
      await executeReplenishment({
        inventoryItemId,
        quantity: recommendation.suggestedQuantity,
        supplierId: recommendation.recommendedSupplier.id
      }).unwrap();
      
      ElMessage.success('智能补货已执行');
    } catch (error) {
      ElMessage.error('补货执行失败');
    }
  };
  
  return (
    <el-card title="智能补货建议" class="replenishment-panel">
      <div class="current-status">
        <Row gutter={16}>
          <Col span={8}>
            <Statistic
              title="当前库存"
              value={item?.currentStock || 0}
              suffix={item?.unit}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="安全库存"
              value={item?.safetyStock || 0}
              suffix={item?.unit}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="预计可用天数"
              value={recommendation?.estimatedDaysOfStock || 0}
              suffix="天"
            />
          </Col>
        </Row>
      </div>
      
      {recommendation && (
        <div class="recommendation" style={{ marginTop: 16 }}>
          <Alert
            message="补货建议"
            description={
              <div>
                <p>建议补货数量：<strong>{recommendation.suggestedQuantity}</strong> {item?.unit}</p>
                <p>推荐供应商：<strong>{recommendation.recommendedSupplier.name}</strong></p>
                <p>预估单价：<strong>¥{recommendation.estimatedUnitPrice}</strong></p>
                <p>预计交期：<strong>{recommendation.estimatedDeliveryDays}</strong> 天</p>
              </div>
            }
            type="info"
            showIcon
            action={
              <el-button type="primary" onClick={handleExecuteReplenishment}>
                一键补货
              </Button>
            }
          />
        </div>
      )}
      
      <div class="forecast-chart" style={{ marginTop: 16 }}>
        <Title level={5}>需求预测</Title>
        <DemandForecastChart data={recommendation?.demandForecast} />
      </div>
    </Card>
  );
};

// 库存详情组件
export const InventoryItemDetail: Vue Component<{ itemId: string }> = ({ itemId }) => {
  const { data: item, isLoading } = useGetInventoryItemDetailQuery(itemId);
  const { data: movements } = useGetStockMovementsQuery(itemId);
  const { data: batches } = useGetInventoryBatchesQuery(itemId);
  
  if (isLoading) {
    return <Skeleton active />;
  }
  
  return (
    <div class="inventory-item-detail">
      <el-card title={item?.productName}>
        <Descriptions column={3}>
          <Descriptions.Item label="商品代码">{item?.productCode}</Descriptions.Item>
          <Descriptions.Item label="当前库存">
            {item?.currentStock} {item?.unit}
          </Descriptions.Item>
          <Descriptions.Item label="可用库存">
            {item?.availableStock} {item?.unit}
          </Descriptions.Item>
          <Descriptions.Item label="预留库存">
            {item?.reservedStock} {item?.unit}
          </Descriptions.Item>
          <Descriptions.Item label="在途库存">
            {item?.inTransitStock} {item?.unit}
          </Descriptions.Item>
          <Descriptions.Item label="安全库存">
            {item?.safetyStock} {item?.unit}
          </Descriptions.Item>
          <Descriptions.Item label="单位成本">
            ¥{item?.unitCost}
          </Descriptions.Item>
          <Descriptions.Item label="库存总值">
            ¥{((item?.currentStock || 0) * (item?.unitCost || 0)).toLocaleString()}
          </Descriptions.Item>
          <Descriptions.Item label="周转率">
            {item?.turnoverRate?.toFixed(2)} 次/年
          </Descriptions.Item>
        </Descriptions>
      </Card>
      
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={12}>
          <el-card title="库存变动记录">
            <StockMovementsTable data={movements || []} />
          </Card>
        </Col>
        <Col span={12}>
          <el-card title="批次信息">
            <InventoryBatchesTable data={batches || []} />
          </Card>
        </Col>
      </Row>
    </div>
  );
};
```

这套库存管理功能实现完全保持了原有设计的智能化特性，包括实时库存跟踪、智能预警系统、基于信任关系的智能补货等核心功能，为超级个人全栈开发者提供了完整的企业级库存管理解决方案。
