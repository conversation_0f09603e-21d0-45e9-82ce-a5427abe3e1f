#!/bin/bash

# Mac开发环境验证脚本
echo "🍎 Mac开发环境验证开始..."
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_command() {
    if command -v $1 &> /dev/null; then
        echo -e "${GREEN}✅ $1 已安装${NC}"
        if [ "$2" != "" ]; then
            echo "   版本: $($1 $2 2>/dev/null || echo '无法获取版本')"
        fi
    else
        echo -e "${RED}❌ $1 未安装${NC}"
        return 1
    fi
}

# 检查Java版本
check_java() {
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
        if [[ $JAVA_VERSION == 17.* ]]; then
            echo -e "${GREEN}✅ Java 17 已安装${NC}"
            echo "   版本: $JAVA_VERSION"
            echo "   JAVA_HOME: $JAVA_HOME"
        else
            echo -e "${YELLOW}⚠️  Java已安装但版本不是17${NC}"
            echo "   当前版本: $JAVA_VERSION"
            echo "   建议安装Java 17"
        fi
    else
        echo -e "${RED}❌ Java 未安装${NC}"
        return 1
    fi
}

# 检查Node.js版本
check_node() {
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version | cut -d'v' -f2)
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1)
        if [ $MAJOR_VERSION -ge 18 ]; then
            echo -e "${GREEN}✅ Node.js 18+ 已安装${NC}"
            echo "   版本: v$NODE_VERSION"
        else
            echo -e "${YELLOW}⚠️  Node.js已安装但版本低于18${NC}"
            echo "   当前版本: v$NODE_VERSION"
            echo "   建议升级到18+"
        fi
    else
        echo -e "${RED}❌ Node.js 未安装${NC}"
        return 1
    fi
}

# 检查PostgreSQL
check_postgresql() {
    if command -v psql &> /dev/null; then
        echo -e "${GREEN}✅ PostgreSQL 已安装${NC}"
        PG_VERSION=$(psql --version | cut -d' ' -f3)
        echo "   版本: $PG_VERSION"
        
        # 检查服务状态
        if brew services list | grep postgresql | grep started &> /dev/null; then
            echo -e "${GREEN}   服务状态: 运行中${NC}"
        else
            echo -e "${YELLOW}   服务状态: 未运行${NC}"
            echo "   启动命令: brew services start postgresql@16"
        fi
    else
        echo -e "${RED}❌ PostgreSQL 未安装${NC}"
        return 1
    fi
}

# 检查Redis
check_redis() {
    if command -v redis-server &> /dev/null; then
        echo -e "${GREEN}✅ Redis 已安装${NC}"
        REDIS_VERSION=$(redis-server --version | cut -d'=' -f2 | cut -d' ' -f1)
        echo "   版本: $REDIS_VERSION"
        
        # 检查服务状态
        if brew services list | grep redis | grep started &> /dev/null; then
            echo -e "${GREEN}   服务状态: 运行中${NC}"
            # 测试连接
            if redis-cli ping &> /dev/null; then
                echo -e "${GREEN}   连接测试: 成功${NC}"
            else
                echo -e "${YELLOW}   连接测试: 失败${NC}"
            fi
        else
            echo -e "${YELLOW}   服务状态: 未运行${NC}"
            echo "   启动命令: brew services start redis"
        fi
    else
        echo -e "${RED}❌ Redis 未安装${NC}"
        return 1
    fi
}

# 检查Docker
check_docker() {
    if command -v docker &> /dev/null; then
        echo -e "${GREEN}✅ Docker 已安装${NC}"
        DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
        echo "   版本: $DOCKER_VERSION"
        
        # 检查Docker是否运行
        if docker info &> /dev/null; then
            echo -e "${GREEN}   状态: 运行中${NC}"
        else
            echo -e "${YELLOW}   状态: 未运行${NC}"
            echo "   请启动Docker Desktop"
        fi
    else
        echo -e "${RED}❌ Docker 未安装${NC}"
        return 1
    fi
}

echo "🔍 检查基础工具..."
echo "----------------------------------"
check_command "brew" "--version"
check_command "git" "--version"
check_command "curl" "--version"

echo ""
echo "☕ 检查Java环境..."
echo "----------------------------------"
check_java

echo ""
echo "🟢 检查Node.js环境..."
echo "----------------------------------"
check_node
check_command "npm" "--version"

echo ""
echo "🐘 检查PostgreSQL..."
echo "----------------------------------"
check_postgresql

echo ""
echo "🔴 检查Redis..."
echo "----------------------------------"
check_redis

echo ""
echo "🐳 检查Docker..."
echo "----------------------------------"
check_docker
check_command "docker-compose" "--version"

echo ""
echo "🛠️  检查开发工具..."
echo "----------------------------------"
if [ -d "/Applications/IntelliJ IDEA.app" ] || [ -d "/Applications/IntelliJ IDEA Ultimate.app" ] || [ -d "/Applications/IntelliJ IDEA CE.app" ]; then
    echo -e "${GREEN}✅ IntelliJ IDEA 已安装${NC}"
else
    echo -e "${YELLOW}⚠️  IntelliJ IDEA 未找到${NC}"
    echo "   安装命令: brew install --cask intellij-idea"
fi

if [ -d "/Applications/Visual Studio Code.app" ]; then
    echo -e "${GREEN}✅ VS Code 已安装${NC}"
else
    echo -e "${YELLOW}⚠️  VS Code 未找到${NC}"
    echo "   安装命令: brew install --cask visual-studio-code"
fi

echo ""
echo "📋 环境验证完成！"
echo "=================================="

# 创建项目目录建议
echo ""
echo "💡 建议的项目目录结构："
echo "mkdir -p ~/Projects/procurement-platform-fullstack"
echo "cd ~/Projects/procurement-platform-fullstack"
echo ""
echo "🚀 准备就绪，可以开始项目初始化！"
